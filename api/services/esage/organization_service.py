import base64
import logging
import secrets
import datetime
from sqlalchemy import or_, and_
from werkzeug.exceptions import NotFound, Conflict
from datetime import UTC, datetime, timedelta
from libs.password import hash_password
from models import AccountRole, Tenant, Department, Account, CasSession, Role, RolePrivilege, TenantAccountJoin, App, \
    ApiToolProvider, Dataset, AccountStatus
from models.engine import db
from services.account_service import TenantService
from events.department_event import department_was_deleted


class OrganizationService:

    # 查询角色列表,name:名称,description:描述
    @classmethod
    def listRoles(cls, page, limit, name):
        accounts = db.session.query(Account).filter(Account.status == AccountStatus.CLOSED.value).all()
        accountIds = []
        for account in accounts:
            accountIds.append(account.id)

        if len(name) > 0:
            roles = Role.query.filter(Role.hide == False,
                                      or_(Role.name.ilike(f"%{name}%"), Role.description.ilike(f"%{name}%"))).order_by(
                Role.created_at.desc()).paginate(
                page=page, per_page=limit, error_out=False)
        else:
            roles = Role.query.filter(Role.hide == False).order_by(Role.created_at.desc()).paginate(page=page,
                                                                                                    per_page=limit,
                                                                                                    error_out=False)

        items = roles.items
        for role in items:
            accounts = db.session.query(AccountRole).filter(AccountRole.account_id.notin_(accountIds),
                                                            AccountRole.role_id == role.id).all()
            role.accountNum = len(accounts)
        # logging.info(f"roles: {roles}")
        return items, roles.total

    # 创建角色
    @classmethod
    def saveRole(cls, args):
        role = Role(
            name=args["name"],
            description=args["description"],
        )
        db.session.add(role)
        db.session.commit()
        return role

    # 编辑角色及角色权限
    @classmethod
    def editRole(cls, role_id: str, args):
        privilege = args["privilege"]
        logging.info(f"privilege: {privilege}")
        if privilege:
            cls.deleteRolePrivilege(role_id)
            for priv in privilege.split(','):
                role_privilege = RolePrivilege(
                    role_id=role_id,
                    privilege=priv,
                )
                db.session.add(role_privilege)
                db.session.commit()
        else:
            filtered_data = {k: v for k, v in args.items() if v is not None}
            db.session.query(Role).filter_by(id=role_id).update(filtered_data)
            db.session.commit()

    # 删除角色
    @classmethod
    def deleteRole(cls, role_id: str):
        accounts = db.session.query(Account).filter(Account.status == AccountStatus.CLOSED.value).all()
        accountIds = []
        for account in accounts:
            accountIds.append(account.id)

        accountRoles = db.session.query(AccountRole).filter(AccountRole.role_id == role_id,
                                                            AccountRole.account_id.notin_(accountIds)).all()
        if len(accountRoles) > 0:
            raise Conflict("当前角色存在关联用户，无法执行删除！")

        role = cls.getRole(role_id, True)
        db.session.delete(role)
        db.session.commit()
        cls.deleteRolePrivilege(role_id)

    # 获取单个角色信息
    @classmethod
    def getRole(cls, role_id, error: bool):
        role = db.session.query(Role).filter(Role.id == role_id).first()
        if not role:
            if error:
                raise NotFound("role not found")
            else:
                return None

        return role

    # 获取单个角色权限
    @classmethod
    def getRolePrivilege(cls, role_id):
        rolePrivileges = db.session.query(RolePrivilege).filter(RolePrivilege.role_id == role_id).all()
        return rolePrivileges

    # 删除角色权限
    @classmethod
    def deleteRolePrivilege(cls, role_id: str):
        rolePrivileges = cls.getRolePrivilege(role_id)
        for rolePrivilege in rolePrivileges:
            db.session.delete(rolePrivilege)
            db.session.commit()

    @classmethod
    def getAccountsByDepartment(cls, department_id: str):
        accounts = db.session.query(Account).filter(Account.department_id == department_id,
                                                    Account.status != AccountStatus.CLOSED.value).all()
        return accounts

    @classmethod
    def getParentDepartment(cls, department_id, departments):
        department = cls.getDepartment(department_id, False)
        if department and department.parent:
            departments.append(department.parent)
            cls.getParentDepartment(department.parent, departments)

    @classmethod
    def getSonDepartment(cls, department_id, departments):
        sons = db.session.query(Department).filter(Department.parent == department_id).all()
        for son in sons:
            departments.append(son.id)
            cls.getSonDepartment(son.id, departments)

    # 查询账号列表,name:名称,status:状态,department_id:部门ID
    @classmethod
    def listAccounts(cls, page, limit, name, status, department_id, orderBy: str, desc: str):
        # name,status,department_id
        filters = [Account.status != AccountStatus.CLOSED.value, Account.hide == False]
        if len(department_id) > 0:
            departments = [department_id]
            # cls.getSonDepartment(department_id, departments)
            logging.info(f"departments: {departments}")
            filters.append(Account.department_id.in_(departments))

        if len(name) > 0:
            filters.append(or_(Account.name.ilike(f"%{name}%"), Account.email.ilike(f"%{name}%")))

        if len(status) > 0:
            filters.append(Account.status.ilike(f"%{status}%"))

        if orderBy == 'status':
            order = Account.status.desc() if desc == 'true' else Account.status.asc()
        elif orderBy == 'name':
            order = Account.name.desc() if desc == 'true' else Account.name.asc()
        else:
            order = Account.updated_at.desc() if desc == 'true' else Account.updated_at.asc()

        # logging.info(f"order: {order}")
        accounts = db.paginate(
            db.select(Account).where(*filters).order_by(order), page=page, per_page=limit, error_out=False)
        for item in accounts.items:
            accountRoles = cls.getAccountRole(item.id)
            item.role = []
            for ar in accountRoles:
                # logging.info(f"{item.name} roleId: {ar.role_id}")
                role = cls.getRole(ar.role_id, False)
                if role:
                    item.role.append({"id": role.id, "name": role.name})

            # logging.info(f"roles: {item.role}")
            if item.department_id is not None:
                department = cls.getDepartment(item.department_id, False)
                item.department = department.name if department else ""

        return accounts.items, accounts.total

    @classmethod
    def getAccountRole(cls, account_id):
        accountRoles = db.session.query(AccountRole).filter(AccountRole.account_id == account_id).all()
        return accountRoles

    @staticmethod
    def validAndInitAccount(user: str, name: str, code: str) -> Account:
        session = db.session.query(CasSession).filter(CasSession.nick == user, CasSession.code == code).first()
        if not session:
            raise Conflict("无效的会话")
        if session.created_at < datetime.now(UTC).replace(tzinfo=None) - timedelta(minutes=5):
            raise Conflict("会话已过期")

        account = db.session.query(Account).filter(Account.email == user).first()
        if not account:
            role = db.session.query(Role).filter(Role.name == '普通用户').first()
            department = db.session.query(Department).filter(Department.name == '组织机构').first()
            args = {
                "email": user,
                "name": name,
                "password": user + "@123!",
                "department_id": department.id if department else None,
                "role_id": role.id if role else None,
            }
            account = OrganizationService.saveAccount(args)

        return account

    # 创建账号
    @classmethod
    def saveAccount(cls, args) -> Account:
        accounts = db.session.query(Account).filter(Account.email == args["email"]).all()
        if len(accounts) > 0:
            raise Conflict("该账号已存在.")

        role_id = args["role_id"]
        rolePrivileges = cls.getRolePrivilege(role_id)
        if len(rolePrivileges) < 1:
            raise Conflict("该角色未分配任何权限")

        account = Account()
        account.interface_language = "zh-Hans"
        account.timezone = "Asia/Shanghai"
        account.name = args["name"]
        account.email = args["email"]
        account.department_id = args["department_id"]
        account.status = AccountStatus.ACTIVE.value
        if args["password"]:
            # generate password salt
            salt = secrets.token_bytes(16)
            base64_salt = base64.b64encode(salt).decode()
            # encrypt password with salt
            password_hashed = hash_password(args["password"], salt)
            base64_password_hashed = base64.b64encode(password_hashed).decode()
            account.password = base64_password_hashed
            account.password_salt = base64_salt

        db.session.add(account)
        db.session.commit()
        # 保存角色关联关系
        ar = AccountRole(
            account_id=account.id,
            role_id=role_id,
        )
        db.session.add(ar)
        db.session.commit()
        # logging.info("saveAccount")
        # 创建个人工作空间
        TenantService.create_tenant(name=account.email, adminUserIds="", account_id=account.id,
                                    description='初始化个人空间', type='personal', is_from_dashboard=True)
        # logging.info("create_tenant")
        return account

    # 启用/禁用账号
    @classmethod
    def enableAccount(cls, account_id: str, enabled: bool):
        if enabled:
            filtered_data = {"status": "active"}
        else:
            filtered_data = {"status": "banned"}

        db.session.query(Account).filter_by(id=account_id).update(filtered_data)
        db.session.commit()
        return filtered_data

    # 编辑账号
    @classmethod
    def editAccount(cls, account_id: str, args):
        role_id = args["role_id"]
        rolePrivileges = cls.getRolePrivilege(role_id)
        if len(rolePrivileges) < 1:
            raise Conflict("该角色未分配任何权限")

        # filtered_data = {k: v for k, v in args.items() if v is not None}
        filtered_data = {"name": args["name"], "department_id": args["department_id"],
                         "updated_at": datetime.datetime.now()}
        if args["password"]:
            # generate password salt
            salt = secrets.token_bytes(16)
            base64_salt = base64.b64encode(salt).decode()
            # encrypt password with salt
            password_hashed = hash_password(args["password"], salt)
            base64_password_hashed = base64.b64encode(password_hashed).decode()
            filtered_data["password"] = base64_password_hashed
            filtered_data["password_salt"] = base64_salt

        db.session.query(Account).filter_by(id=account_id).update(filtered_data)
        db.session.commit()
        # 更新角色关联
        ars = db.session.query(AccountRole).filter(AccountRole.account_id == account_id).all()
        for ar in ars:
            db.session.delete(ar)
            db.session.commit()

        ar = AccountRole(
            account_id=account_id,
            role_id=role_id,
        )
        db.session.add(ar)
        db.session.commit()

    # 删除账号
    @classmethod
    def deleteAccount(cls, account_id: str, deleteComplete: bool):
        if not deleteComplete:  # 逻辑删除,只更新状态到软删除
            filtered_data = {"status": "closed"}
            db.session.query(Account).filter_by(id=account_id).update(filtered_data)
            db.session.commit()
            return

        account = cls.getAccount(account_id)
        db.session.delete(account)
        db.session.commit()
        # 删除角色关联
        ars = db.session.query(AccountRole).filter(AccountRole.account_id == account_id).all()
        for ar in ars:
            db.session.delete(ar)
            db.session.commit()
        # 删除工作空间
        tenantAccountJoin = db.session.query(TenantAccountJoin).filter(
            TenantAccountJoin.account_id == account.id).first()
        if tenantAccountJoin is not None:
            tenant = db.session.query(Tenant).filter(Tenant.id == tenantAccountJoin.tenant_id).first()
            if tenant is not None:
                db.session.delete(tenant)
                db.session.commit()

            db.session.delete(tenantAccountJoin)
            db.session.commit()

    # 获取单个账号信息
    @classmethod
    def getAccount(cls, account_id):
        account = db.session.query(Account).filter(Account.id == account_id).first()
        if not account:
            raise NotFound("account not found")
        return account

    @classmethod
    def getAccountByEmail(cls, email: str):
        account = db.session.query(Account).filter(Account.email == email).first()
        if not account:
            raise NotFound("account not found")
        accountJoin = db.session.query(TenantAccountJoin).filter(TenantAccountJoin.account_id == account.id).first()
        account.current_tenant_id = accountJoin.tenant_id
        return account

    # 查询账号拥有的资源数量
    @classmethod
    def get_account_resourceNum(cls, account_id: str):
        tenant_id_subquery = db.session.query(TenantAccountJoin.tenant_id).filter(
            TenantAccountJoin.account_id == account_id).subquery()
        # accountJoin = db.session.query(TenantAccountJoin).filter(TenantAccountJoin.account_id == account_id).first()
        data = []
        # 智能体
        apps = db.session.query(App).filter(App.tenant_id.in_(tenant_id_subquery), App.mode != 'workflow').all()
        data.append({"resourceType": "App", "resourceNum": len(apps)})
        # 工作流
        workflows = db.session.query(App).filter(App.tenant_id.in_(tenant_id_subquery), App.mode == 'workflow').all()
        data.append({"resourceType": "Workflow", "resourceNum": len(workflows)})
        # 插件
        tools = db.session.query(ApiToolProvider).filter(ApiToolProvider.tenant_id.in_(tenant_id_subquery)).all()
        data.append({"resourceType": "Tool", "resourceNum": len(tools)})
        # 知识库
        datasets = db.session.query(Dataset).filter(Dataset.tenant_id.in_(tenant_id_subquery)).all()
        data.append({"resourceType": "Dataset", "resourceNum": len(datasets)})

        return data

    # 查询账号拥有的权限
    @classmethod
    def get_account_privilege(cls, account_id: str) -> list:
        """
        获取账号权限
        :param account_id: 账号ID
        :return: 权限列表
        """
        # 查询账号角色
        role_ids = [ar.role_id for ar in
                    db.session.query(AccountRole).filter(AccountRole.account_id == account_id).all()]
        if not role_ids:
            return []
        # 查询角色权限
        privileges = db.session.query(RolePrivilege.privilege).filter(RolePrivilege.role_id.in_(role_ids)).all()

        return list({p[0] for p in privileges})

    # 查询部门树
    @classmethod
    def getDepartmentTree(cls):
        sql_query = "select*from department where parent is null"
        response_data = []
        with db.engine.begin() as conn:
            rs = conn.execute(db.text(sql_query))
            for one in rs:
                oneLevel = dict(id=str(one.id), name=str(one.name))
                oneLevel["sons"] = []
                accountNum1 = len(cls.getAccountsByDepartment(one.id))
                seconds = db.session.query(Department).filter(Department.parent == one.id).all()
                for second in seconds:
                    secondLevel = dict(id=str(second.id), name=str(second.name))
                    secondLevel["sons"] = []
                    accountNum2 = len(cls.getAccountsByDepartment(second.id))
                    threes = db.session.query(Department).filter(Department.parent == second.id).all()
                    for three in threes:
                        threeLevel = dict(id=str(three.id), name=str(three.name))
                        threeLevel["sons"] = []
                        accountNum3 = len(cls.getAccountsByDepartment(three.id))
                        fours = db.session.query(Department).filter(Department.parent == three.id).all()
                        for four in fours:
                            fourLevel = dict(id=str(four.id), name=str(four.name))
                            fourLevel["sons"] = []
                            accountNum4 = len(cls.getAccountsByDepartment(four.id))
                            fives = db.session.query(Department).filter(Department.parent == four.id).all()
                            for five in fives:
                                accountNum5 = len(cls.getAccountsByDepartment(five.id))
                                fiveLevel = {"id": str(five.id), "name": str(five.name), "accountNum": accountNum5}
                                fourLevel["sons"].append(fiveLevel)
                                # accountNum4 += accountNum5
                            fourLevel["accountNum"] = accountNum4
                            threeLevel["sons"].append(fourLevel)
                            # accountNum3 += accountNum4
                        threeLevel["accountNum"] = accountNum3
                        secondLevel["sons"].append(threeLevel)
                        # accountNum2 += accountNum3
                    secondLevel["accountNum"] = accountNum2
                    oneLevel["sons"].append(secondLevel)
                    # accountNum1 += accountNum2
                oneLevel["accountNum"] = accountNum1
                response_data.append(oneLevel)

        return response_data

    # 查询部门列表,department_id:上级部门ID
    @classmethod
    def listDepartment(cls, department_id):
        logging.info(f"department: {department_id}")
        if department_id:
            departments = db.session.query(Department).filter(Department.parent == department_id).all()
            # for depart in departments:
            #     accounts = db.session.query(Account).filter(Account.department_id == depart.id).all()
            #     depart.accountNum = len(accounts)
            return departments
        else:
            # accountNum = "select count(*) from accounts where department_id is not null"
            sql_query = "select*from department where parent is null"
            response_data = []
            with db.engine.begin() as conn:
                # result = conn.execute(db.text(accountNum))
                # accountNum = result.first()[0]
                rs = conn.execute(db.text(sql_query))
                for i in rs:
                    response_data.append(
                        {"id": str(i.id), "name": str(i.name), "created_at": i.created_at})

            return response_data

    # 创建部门
    @classmethod
    def saveDepartment(cls, args):
        name = args["name"]
        parent = args["parent"]
        if parent:
            cls.getDepartment(parent, True)
            departments = db.session.query(Department).filter(Department.parent == parent,
                                                              Department.name == name).all()
            if len(departments) > 0:
                raise Conflict("相同名称已存在")

        department = Department(
            name=name,
            parent=parent,
        )
        db.session.add(department)
        db.session.commit()
        return department

    # 编辑部门
    @classmethod
    def editDepartment(cls, department_id: str, args):
        department = cls.getDepartment(department_id, True)
        name = args["name"]
        if department.parent:
            departments = db.session.query(Department).filter(Department.parent == department.parent,
                                                              Department.name == name,
                                                              Department.id != department_id).all()
            if len(departments) > 0:
                raise Conflict("相同名称已存在")

        filtered_data = {k: v for k, v in args.items() if v is not None}
        db.session.query(Department).filter_by(id=department_id).update(filtered_data)
        db.session.commit()
        return filtered_data

    # 删除部门
    @classmethod
    def deleteDepartment(cls, department_id: str):
        department = cls.getDepartment(department_id, True)
        departments = cls.listDepartment(department_id)
        if len(departments) > 0:
            raise Conflict("该部门下存在子部门,不允许删除")

        accounts = db.session.query(Account).filter(Account.department_id == department_id,
                                                    Account.status != AccountStatus.CLOSED.value).all()
        if len(accounts) > 0:
            raise Conflict("该部门下存在关联账号,不允许删除")

        department_was_deleted.send(department)

        db.session.delete(department)
        db.session.commit()

    # 获取单个部门信息
    @classmethod
    def getDepartment(cls, department_id: str, error: bool):
        department = db.session.query(Department).filter(Department.id == department_id).first()
        if not department:
            if error:
                raise NotFound("department not found")
            else:
                return None

        return department

    # 通用校验名称是否存在
    @classmethod
    def validNameExist(cls, name: str, type: str, id: str):
        model_map = {
            "account": (Account, Account.email),
            "department": (Department, Department.name),
            "role": (Role, Role.name),
        }

        if type not in model_map:
            raise ValueError(f"Invalid type: {type}. Expected one of {list(model_map.keys())}")

        model_class, name_field = model_map[type]
        query = db.session.query(model_class).filter(name_field == name)

        if id is not None:
            query = query.filter(model_class.id != id)

        obj = query.first()
        return bool(obj)
