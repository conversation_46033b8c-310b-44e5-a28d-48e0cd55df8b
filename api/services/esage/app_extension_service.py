import logging
from typing import Optional
from werkzeug.exceptions import NotFound, Conflict
from flask_sqlalchemy.pagination import Pagination
from sqlalchemy import or_, text
from services.esage.organization_service import OrganizationService
from libs.login import current_user
from models import Tenant, ConfHomePage, ConfAppCenter, TenantAccountJoin
from extensions.ext_database import db
from models.app_extension import AppAuthorization, AppExtension, Category, AuthTypeEnum
from models.model import App, Site, InstalledApp
from models.account import Department, Account


class AppExtensionService:
    def get_pagination_app_authorization(
            self,
            user_id: str,
            tenant_id: str,
            args: dict,
    ) -> Pagination | None:
        """
        Get paginated app authorizations.
        """

        query = db.session.query(AppAuthorization)
        # 先 outerjoin Account 和 Department，确保后续 filter 可用其字段
        query = (
            query.outerjoin(Department, AppAuthorization.department_id == Department.id)
            .outerjoin(Account, AppAuthorization.account_id == Account.id)
            .order_by(AppAuthorization.created_at.desc())
        )

        if args.get("app_id"):
            query = query.filter(AppAuthorization.app_id == args.get("app_id"))

        if args.get("name"):
            query = query.filter(
                or_(
                    (AppAuthorization.auth_type == AuthTypeEnum.ACCOUNT.value) & (
                            Account.status == "active") & Account.name.ilike(f"%{args.get('name')}%"),
                    (AppAuthorization.auth_type == AuthTypeEnum.DEPARTMENT.value) & Department.name.ilike(
                        f"%{args.get('name')}%"),
                )
            )
        else:
            query = query.filter(
                or_(
                    (AppAuthorization.auth_type == AuthTypeEnum.ACCOUNT.value) & (Account.status == "active"),
                    (AppAuthorization.auth_type == AuthTypeEnum.DEPARTMENT.value),
                )
            )

        # 加 .distinct() 避免多次 outerjoin 导致的重复
        app_auth_pagination = db.paginate(
            query.distinct(),
            page=args.get("page"),
            per_page=args.get("limit"),
            error_out=False,
        )

        return app_auth_pagination

    def create_app_authorization(
            self,
            user_id: str,
            tenant_id: str,
            args: dict,
    ) -> None:
        """
        Create app authorization.
        """

        def _check_auth_exists(auth_type, account_id, department_id):
            if auth_type == "ACCOUNT":
                return any(auth.account_id == account_id for auth in current_auths)
            elif auth_type == "DEPARTMENT":
                return any(
                    auth.department_id == department_id for auth in current_auths
                )
            return False

        app_id = args["app_id"]
        current_auths = (
            db.session.query(AppAuthorization)
            .filter(
                AppAuthorization.app_id == app_id,
            )
            .all()
        )

        for auth in args["authorizations"]:
            if not _check_auth_exists(
                    auth["auth_type"],
                    auth.get("account_id"),
                    auth.get("department_id"),
            ):
                new_auth = AppAuthorization(
                    app_id=app_id,
                    account_id=auth.get("account_id"),
                    department_id=auth.get("department_id"),
                    auth_type=auth["auth_type"],
                    created_at=db.func.now(),
                    updated_at=db.func.now(),
                )
                db.session.add(new_auth)

        db.session.commit()

        app_authorizations = (
            db.session.query(AppAuthorization)
            .filter(
                AppAuthorization.app_id == app_id,
            )
            .all()
        )

        return app_authorizations

    def delete_app_authorization(
            self,
            user_id: str,
            tenant_id: str,
            args: dict,
    ) -> None:
        """
        Delete app authorization.
        """

        app_id = args["app_id"]
        auth_ids = args["auth_ids"]

        db.session.query(AppAuthorization).filter(AppAuthorization.id.in_(auth_ids),
                                                  AppAuthorization.app_id == app_id).delete()
        db.session.commit()

        app_authorizations = (
            db.session.query(AppAuthorization)
            .filter(
                AppAuthorization.app_id == app_id,
            )
            .all()
        )
        return app_authorizations

    # ---------- Category CRUD ----------

    def create_category(self, name: str, description: Optional[str] = None) -> Category:
        category = Category(name=name, description=description)
        db.session.add(category)
        db.session.commit()
        return category

    def get_category(self, category_id: str) -> Optional[Category]:
        return db.session.query(Category).filter_by(id=category_id).first()

    def list_categories(self) -> list[Category]:
        return db.session.query(Category).order_by(Category.sort.asc(), Category.created_at.desc()).all()

    def update_category(self, category_id: str, name: Optional[str] = None, description: Optional[str] = None) -> \
            Optional[Category]:
        category = db.session.query(Category).filter_by(id=category_id).first()
        if not category:
            return None
        if name is not None:
            category.name = name
        if description is not None:
            category.description = description
        db.session.commit()
        return category

    def delete_category(self, category_id: str) -> bool:
        category = db.session.query(Category).filter_by(id=category_id).first()
        if not category:
            return False
        db.session.delete(category)
        db.session.commit()
        return True

    # ---------- AppExtension CRUD ----------

    def create_app_extension(self, app_id: str, type_: str, category_id: Optional[str],
                             description: Optional[str] = None) -> AppExtension:
        # 官方账号发布的应用type设置为 builtin
        if current_user.email == '<EMAIL>':
            type_ = 'builtin'

        app_extension = AppExtension(
            app_id=app_id,
            type=type_,
            category_id=category_id,
            description=description
        )
        db.session.add(app_extension)
        db.session.commit()
        return app_extension

    def get_app_extension(self, app_id: str, category_id: Optional[str] = None) -> Optional[AppExtension]:
        query = db.session.query(AppExtension).filter_by(app_id=app_id)
        if category_id:
            query = query.filter_by(category_id=category_id)
        return query.first()

    def list_app_extensions(self, app_id: Optional[str] = None) -> list[AppExtension]:
        query = db.session.query(AppExtension)
        if app_id:
            query = query.filter_by(app_id=app_id)
        return query.all()

    def update_app_extension(self, app_id: str, category_id: Optional[str], **kwargs) -> Optional[AppExtension]:
        query = db.session.query(AppExtension).filter_by(app_id=app_id)
        if category_id:
            query = query.filter_by(category_id=category_id)
        app_extension = query.first()
        if not app_extension:
            return None
        for key, value in kwargs.items():
            if hasattr(app_extension, key):
                setattr(app_extension, key, value)
        db.session.commit()
        return app_extension

    def delete_app_extension(self, app_id: str, session=None) -> int:
        session = session or db.session
        deleted_count = session.query(AppExtension).filter_by(app_id=app_id).delete()
        return deleted_count

    def bulk_create_app_extensions(self, app_id: str, type_: str, category_ids: list[str],
                                   description: Optional[str] = None, session=None) -> list[AppExtension]:
        # 官方账号发布的应用type设置为 builtin
        if current_user.email == '<EMAIL>':
            type_ = 'builtin'

        session = session or db.session
        extensions = []
        for category_id in category_ids:
            ext = AppExtension(
                app_id=app_id,
                type=type_,
                category_id=category_id,
                description=description
            )
            session.add(ext)
            extensions.append(ext)
        return extensions

    def list_app_extensions_by_category(self, category_id: str) -> list[AppExtension]:
        """
        根据 category_id 查询所有 app_extensions
        """
        return db.session.query(AppExtension).filter_by(category_id=category_id).all()

    def list_app_extensions_with_app_and_site_by_category(
            self, category_id, page=1, limit=20, department_id=None, account_id=None, name=None
    ):
        """
        修复分页逻辑：先查所有 app_id，分页后再查详情，保证 total 和返回条数一致。
        """
        # 1. 先查所有 app_id
        app_id_query = db.session.query(App.id)
        app_id_query = app_id_query.join(AppExtension, App.id == AppExtension.app_id)
        app_id_query = app_id_query.filter(AppExtension.category_id == category_id)
        auth_filters = []
        if department_id:
            auth_filters.append(AppAuthorization.department_id == department_id)
        if account_id:
            auth_filters.append(AppAuthorization.account_id == account_id)
            auth_filters.append(App.created_by == account_id)
        if auth_filters:
            app_id_query = app_id_query.outerjoin(AppAuthorization, App.id == AppAuthorization.app_id)
            app_id_query = app_id_query.filter(or_(*auth_filters))
            logging.info(f"Applied {len(auth_filters)} auth filters for category {category_id}")
        else:
            logging.info(f"No auth filters applied for category {category_id} - querying all apps")
        if name:
            app_id_query = app_id_query.filter(App.name.ilike(f"%{name}%"))
            logging.info(f"Applied name filter: {name}")
        app_id_query = app_id_query.distinct()
        all_app_ids = [row[0] for row in app_id_query.all()]
        total = len(all_app_ids)
        logging.info(f"Total count for category {category_id}: {total}")
        # 2. 排序、分页（在 App 实体上做）
        if all_app_ids:
            apps = (
                db.session.query(App)
                .filter(App.id.in_(all_app_ids))
                .order_by(App.created_at.desc())
                .offset((page - 1) * limit)
                .limit(limit)
                .all()
            )
            app_ids = [app.id for app in apps]
        else:
            apps = []
            app_ids = []
        # 3. 批量查每个 app 的第一条 AppExtension
        exts = (
            db.session.query(AppExtension)
            .filter(AppExtension.app_id.in_(app_ids))
            .order_by(AppExtension.app_id, AppExtension.created_at.asc())
            .all()
        )
        ext_dict = {}
        for ext in exts:
            if ext.app_id not in ext_dict:
                ext_dict[ext.app_id] = ext
        # 4. 批量查每个 app 的第一条 Site
        sites = (
            db.session.query(Site)
            .filter(Site.app_id.in_(app_ids))
            .order_by(Site.app_id, Site.created_at.asc())
            .all()
        )
        site_dict = {}
        for site in sites:
            if site.app_id not in site_dict:
                site_dict[site.app_id] = site
        # 5. 组装结果，app为完整实体
        result = []
        apps_dict = {app.id: app for app in apps}
        for app_id in app_ids:
            result.append((ext_dict.get(app_id), apps_dict.get(app_id), site_dict.get(app_id)))
        return result, total

    def list_app_extensions_with_site_by_category(self, category_id: str):
        """
        查询某个场景下所有扩展，并带上Site信息
        """
        from models.model import Site  # 根据你的实际路径调整
        results = (
            db.session.query(
                AppExtension,
                Site
            )
            .outerjoin(Site, Site.app_id == AppExtension.app_id)
            .filter(AppExtension.category_id == category_id)
            .all()
        )
        # 返回 [(AppExtension, Site), ...]
        return results

    def list_my_apps_with_first_extension_and_site(
            self, page=1, limit=20, account_id=None, name=None
    ):
        """
        查询自己创建且已发布（app_extension有数据）的唯一 app，分页，组装每个 app 的第一条 AppExtension 和 Site
        """
        # 1. 保持原有 query 逻辑
        query = (
            db.session.query(App.id)
            .join(AppExtension, App.id == AppExtension.app_id)
            .filter(App.created_by == account_id)
        )
        if name:
            query = query.filter(
                or_(
                    App.name.ilike(f"%{name}%"),
                    App.description.ilike(f"%{name}%"),
                )
            )
        query = query.distinct()
        all_app_ids = [row[0] for row in query.all()]
        total = len(all_app_ids)

        # 2. 排序、分页（在 App 实体上做）
        if all_app_ids:
            apps = (
                db.session.query(App)
                .filter(App.id.in_(all_app_ids))
                .order_by(App.created_at.desc())
                .offset((page - 1) * limit)
                .limit(limit)
                .all()
            )
            app_ids = [app.id for app in apps]
        else:
            apps = []
            app_ids = []

        # 3. 批量查每个 app 的第一条 AppExtension
        exts = (
            db.session.query(AppExtension)
            .filter(AppExtension.app_id.in_(app_ids))
            .order_by(AppExtension.app_id, AppExtension.created_at.asc())
            .all()
        )
        ext_dict = {}
        for ext in exts:
            if ext.app_id not in ext_dict:
                ext_dict[ext.app_id] = ext

        # 4. 批量查每个 app 的第一条 Site
        sites = (
            db.session.query(Site)
            .filter(Site.app_id.in_(app_ids))
            .order_by(Site.app_id, Site.created_at.asc())
            .all()
        )
        site_dict = {}
        for site in sites:
            if site.app_id not in site_dict:
                site_dict[site.app_id] = site

        # 5. 组装结果，app为完整实体
        result = []
        apps_dict = {app.id: app for app in apps}
        for app_id in app_ids:
            result.append((ext_dict.get(app_id), apps_dict.get(app_id), site_dict.get(app_id)))
        return result, total

    def list_accessible_apps_with_first_extension_and_site(
            self, page=1, limit=20, department_id=None, account_id=None, name=None
    ):
        """
        查询当前用户有权限看到的所有应用（去重 app_id），分页，组装每个 app 的第一条 AppExtension 和 Site，返回完整 app 实体
        """
        # 1. 保持原有 query 逻辑，先查所有 app_id
        app_id_query = (
            db.session.query(App.id)
            .join(AppExtension, App.id == AppExtension.app_id)
        )

        auth_filters = []
        if department_id:
            auth_filters.append(AppAuthorization.department_id == department_id)
        if account_id:
            auth_filters.append(AppAuthorization.account_id == account_id)
            auth_filters.append(App.created_by == account_id)
        if auth_filters:
            app_id_query = app_id_query.outerjoin(AppAuthorization, App.id == AppAuthorization.app_id)
            app_id_query = app_id_query.filter(or_(*auth_filters))

        if name:
            app_id_query = app_id_query.filter(
                or_(
                    App.name.ilike(f"%{name}%"),
                    App.description.ilike(f"%{name}%"),
                )
            )

        app_id_query = app_id_query.distinct()
        all_app_ids = [row[0] for row in app_id_query.all()]
        total = len(all_app_ids)

        # 2. 排序、分页（在 App 实体上做）
        if all_app_ids:
            apps = (
                db.session.query(App)
                .filter(App.id.in_(all_app_ids))
                .order_by(App.created_at.desc())
                .offset((page - 1) * limit)
                .limit(limit)
                .all()
            )
            app_ids = [app.id for app in apps]
        else:
            apps = []
            app_ids = []

        # 3. 批量查每个 app 的第一条 AppExtension
        exts = (
            db.session.query(AppExtension)
            .filter(AppExtension.app_id.in_(app_ids))
            .order_by(AppExtension.app_id, AppExtension.created_at.asc())
            .all()
        )
        ext_dict = {}
        for ext in exts:
            if ext.app_id not in ext_dict:
                ext_dict[ext.app_id] = ext

        # 4. 批量查每个 app 的第一条 Site
        sites = (
            db.session.query(Site)
            .filter(Site.app_id.in_(app_ids))
            .order_by(Site.app_id, Site.created_at.asc())
            .all()
        )
        site_dict = {}
        for site in sites:
            if site.app_id not in site_dict:
                site_dict[site.app_id] = site

        # 5. 组装结果，app为完整实体
        result = []
        apps_dict = {app.id: app for app in apps}
        for app_id in app_ids:
            result.append((ext_dict.get(app_id), apps_dict.get(app_id), site_dict.get(app_id)))
        return result, total

    @classmethod
    def listOfficialApps(cls, page, limit, name):
        # published_ids = set(row[0] for row in db.session.query(AppExtension.app_id).all())
        # 获取官方应用空间，并做异常处理
        tenant = db.session.query(Tenant).filter(Tenant.type == "official").first()
        if not tenant:
            return [], 0

        # 使用子查询替代内存中的集合，提升性能
        published_ids_subquery = db.session.query(AppExtension.app_id).subquery()

        # 构建基础过滤条件
        filters = [
            App.tenant_id == tenant.id,
            App.id.in_(published_ids_subquery)
        ]

        if name:
            filters.append(App.name.ilike(f"%{name}%"))

        query = db.select(App).where(*filters).order_by(App.created_at.desc())
        apps = db.paginate(query, page=page, per_page=limit, error_out=False)
        return apps.items if apps else [], apps.total if apps else 0

    @classmethod
    def getHomePageConfig(cls):
        return db.session.query(ConfHomePage).all()

    @classmethod
    def getAppCenterConfig(cls):
        return db.session.query(ConfAppCenter).all()

    @classmethod
    def editHomePageConfig(cls, args):
        """
        编辑或创建首页配置项。
        """
        if args["id"]:
            # 查询指定ID的配置项，并更新其属性
            conf = db.session.query(ConfHomePage).filter(ConfHomePage.id == args["id"]).first()
            if conf:
                conf.type = args["type"]
                conf.content = args["content"]
                # conf.sequence = args["sequence"]
                # logging.info("type:%s",conf.type)
                db.session.commit()
            else:
                raise Conflict("设置项不存在")
        else:
            raise Conflict("id为必传")
            # conf = ConfHomePage(type=args["type"], content=args["content"], sequence=args["sequence"])
            # db.session.add(conf)
            # db.session.commit()

    @classmethod
    def editAppCenterConfig(cls, args):
        if args["id"]:
            conf = db.session.query(ConfAppCenter).filter(ConfAppCenter.id == args["id"]).first()
            if conf:
                conf.type = args["type"]
                conf.content = args["content"]
                # conf.sequence = args["sequence"]
                db.session.commit()
            else:
                raise Conflict("设置项不存在")
        else:
            raise Conflict("id为必传")
            # conf = ConfAppCenter(type=args["type"], content=args["content"], sequence=args["sequence"])
            # db.session.add(conf)
            # db.session.commit()

    @classmethod
    def accessibel(cls, app_id: str) -> str:
        # installed_app = db.session.query(InstalledApp).filter_by(id=install_app_id).first()
        # if not installed_app:
        #     raise NotFound("请求的应用不存在")

        # 使用子查询替代内存中的集合，提升性能
        published_ids_subquery = db.session.query(AppExtension.app_id).subquery()
        # 校验应用是否发布
        app = db.session.query(App).filter(App.id == app_id, App.id.in_(published_ids_subquery)).first()
        if not app:
            raise Conflict("请求的应用不存在或已下架！")
        # 官方账号的空间
        # offical = db.session.query(Tenant).filter(Tenant.type == "official").first()
        # 校验是否有访问权限
        # if app.tenant_id == offical.id:
        #     pass
        # logging.info("app.created_by:%s,current_user.id:%s", app.created_by, current_user.id)
        if app.created_by == current_user.id:
            pass
        else:
            authAccount = db.session.query(AppAuthorization).filter(AppAuthorization.app_id == app_id,
                                                                    AppAuthorization.account_id == current_user.id).first()
            if not authAccount:
                departments = [current_user.department_id]
                OrganizationService.getParentDepartment(current_user.department_id, departments)
                authDepartment = db.session.query(AppAuthorization).filter(AppAuthorization.app_id == app_id,
                                                                           AppAuthorization.department_id.in_(
                                                                               departments)).first()
                if not authDepartment:
                    raise Conflict("无使用权限，请联系管理员授权！")

        installed_app = db.session.query(InstalledApp).filter_by(app_id=app.id,
                                                                 tenant_id=current_user.current_tenant_id).first()
        if not installed_app:
            # 没有则自动新增
            installed_app = InstalledApp(app_id=app.id, tenant_id=current_user.current_tenant_id,
                                         app_owner_tenant_id=app.tenant_id,
                                         is_pinned=False)
            db.session.add(installed_app)
            db.session.commit()
        return installed_app.id
