import base64
import json
import logging
import random
import secrets
import uuid
from datetime import UTC, datetime, timedelta
from hashlib import sha256
from typing import Any, Optional, cast

from flask_login import current_user
from pydantic import BaseModel
from sqlalchemy import func, case
from sqlalchemy.orm import Session
from werkzeug.exceptions import Unauthorized

from configs import dify_config
from constants.languages import language_timezone_mapping, languages
from events.tenant_event import tenant_was_created
from extensions.ext_database import db
from extensions.ext_redis import redis_client
from libs.helper import RateLimiter, TokenManager
from libs.passport import PassportService
from libs.password import compare_password, hash_password, valid_password
from libs.rsa import generate_key_pair
from models.account import (
    Account,
    AccountIntegrate,
    AccountStatus,
    Tenant,
    TenantAccountJoin,
    TenantAccountRole,
    TenantStatus, )
from models.model import DifySetup
from services.billing_service import BillingService
from services.errors.account import (
    AccountAlreadyInTenantError,
    AccountCLOSEDError,
    Account<PERSON>annedError,
    AccountNotFoundError,
    AccountNotLinkTenantError,
    AccountPasswordError,
    AccountRegisterError,
    CannotOperateSelfError,
    CurrentPasswordIncorrectError,
    InvalidActionError,
    LinkAccountIntegrateError,
    MemberNotInTenantError,
    NoPermissionError,
    RoleAlreadyAssignedError,
    TenantNotFoundError,
)
from services.errors.workspace import WorkSpaceNotAllowedCreateError
from services.feature_service import FeatureService
from services.model_provider_service import ModelProviderService
from services.plugin.plugin_service import PluginService
from tasks.delete_account_task import delete_account_task
from tasks.mail_account_deletion_task import send_account_deletion_verification_code
from tasks.mail_email_code_login import send_email_code_login_mail_task
from tasks.mail_invite_member_task import send_invite_member_mail_task
from tasks.mail_reset_password_task import send_reset_password_mail_task


class TokenPair(BaseModel):
    access_token: str
    refresh_token: str


REFRESH_TOKEN_PREFIX = "refresh_token:"
ACCOUNT_REFRESH_TOKEN_PREFIX = "account_refresh_token:"
REFRESH_TOKEN_EXPIRY = timedelta(days=dify_config.REFRESH_TOKEN_EXPIRE_DAYS)


class AccountService:
    reset_password_rate_limiter = RateLimiter(prefix="reset_password_rate_limit", max_attempts=1, time_window=60 * 1)
    email_code_login_rate_limiter = RateLimiter(
        prefix="email_code_login_rate_limit", max_attempts=1, time_window=60 * 1
    )
    email_code_account_deletion_rate_limiter = RateLimiter(
        prefix="email_code_account_deletion_rate_limit", max_attempts=1, time_window=60 * 1
    )
    LOGIN_MAX_ERROR_LIMITS = 5
    FORGOT_PASSWORD_MAX_ERROR_LIMITS = 5

    @staticmethod
    def _get_refresh_token_key(refresh_token: str) -> str:
        return f"{REFRESH_TOKEN_PREFIX}{refresh_token}"

    @staticmethod
    def _get_account_refresh_token_key(account_id: str) -> str:
        return f"{ACCOUNT_REFRESH_TOKEN_PREFIX}{account_id}"

    @staticmethod
    def _store_refresh_token(refresh_token: str, account_id: str) -> None:
        redis_client.setex(AccountService._get_refresh_token_key(refresh_token), REFRESH_TOKEN_EXPIRY, account_id)
        redis_client.setex(
            AccountService._get_account_refresh_token_key(account_id), REFRESH_TOKEN_EXPIRY, refresh_token
        )

    @staticmethod
    def _delete_refresh_token(refresh_token: str, account_id: str) -> None:
        redis_client.delete(AccountService._get_refresh_token_key(refresh_token))
        redis_client.delete(AccountService._get_account_refresh_token_key(account_id))

    @staticmethod
    def load_user(user_id: str) -> None | Account:
        account = db.session.query(Account).filter_by(id=user_id).first()
        if not account:
            return None

        if account.status == AccountStatus.BANNED.value:
            raise Unauthorized("Account is banned.")

        current_tenant = TenantAccountJoin.query.filter_by(account_id=account.id, current=True).first()
        if current_tenant:
            account.current_tenant_id = current_tenant.tenant_id
        else:
            available_ta = (
                TenantAccountJoin.query.filter_by(account_id=account.id).order_by(TenantAccountJoin.id.asc()).first()
            )
            if not available_ta:
                return None

            account.current_tenant_id = available_ta.tenant_id
            available_ta.current = True
            db.session.commit()

        if datetime.now(UTC).replace(tzinfo=None) - account.last_active_at > timedelta(minutes=10):
            account.last_active_at = datetime.now(UTC).replace(tzinfo=None)
            db.session.commit()

        return cast(Account, account)

    @staticmethod
    def get_account_jwt_token(account: Account) -> str:
        exp_dt = datetime.now(UTC) + timedelta(minutes=dify_config.ACCESS_TOKEN_EXPIRE_MINUTES)
        exp = int(exp_dt.timestamp())
        payload = {
            "user_id": account.id,
            "exp": exp,
            "iss": dify_config.EDITION,
            "sub": "Console API Passport",
        }

        token: str = PassportService().issue(payload)
        return token

    @staticmethod
    def authenticate(email: str, password: str, invite_token: Optional[str] = None) -> Account:
        """authenticate account with email and password"""

        account = db.session.query(Account).filter_by(email=email).first()
        if not account:
            raise AccountNotFoundError()

        if account.status == AccountStatus.CLOSED.value:
            raise AccountCLOSEDError()

        if account.status == AccountStatus.BANNED.value:
            raise AccountBannedError()

        if password and invite_token and account.password is None:
            # if invite_token is valid, set password and password_salt
            salt = secrets.token_bytes(16)
            base64_salt = base64.b64encode(salt).decode()
            password_hashed = hash_password(password, salt)
            base64_password_hashed = base64.b64encode(password_hashed).decode()
            account.password = base64_password_hashed
            account.password_salt = base64_salt

        if account.password is None or not compare_password(password, account.password, account.password_salt):
            raise AccountPasswordError("Invalid email or password.")

        if account.status == AccountStatus.PENDING.value:
            account.status = AccountStatus.ACTIVE.value
            account.initialized_at = datetime.now(UTC).replace(tzinfo=None)

        db.session.commit()

        return cast(Account, account)

    @staticmethod
    def update_account_password(account, password, new_password):
        """update account password"""
        if account.password and not compare_password(password, account.password, account.password_salt):
            raise CurrentPasswordIncorrectError("Current password is incorrect.")

        # may be raised
        valid_password(new_password)

        # generate password salt
        salt = secrets.token_bytes(16)
        base64_salt = base64.b64encode(salt).decode()

        # encrypt password with salt
        password_hashed = hash_password(new_password, salt)
        base64_password_hashed = base64.b64encode(password_hashed).decode()
        account.password = base64_password_hashed
        account.password_salt = base64_salt
        db.session.commit()
        return account

    @staticmethod
    def create_account(
            email: str,
            name: str,
            interface_language: str,
            password: Optional[str] = None,
            interface_theme: str = "light",
            is_setup: Optional[bool] = False,
    ) -> Account:
        """create account"""
        if not FeatureService.get_system_features().is_allow_register and not is_setup:
            from controllers.console.error import AccountNotFound

            raise AccountNotFound()

        if dify_config.BILLING_ENABLED and BillingService.is_email_in_freeze(email):
            raise AccountRegisterError(
                description=(
                    "This email account has been deleted within the past "
                    "30 days and is temporarily unavailable for new account registration"
                )
            )

        account = Account()
        account.email = email
        account.name = name

        if password:
            # generate password salt
            salt = secrets.token_bytes(16)
            base64_salt = base64.b64encode(salt).decode()

            # encrypt password with salt
            password_hashed = hash_password(password, salt)
            base64_password_hashed = base64.b64encode(password_hashed).decode()

            account.password = base64_password_hashed
            account.password_salt = base64_salt

        account.interface_language = interface_language
        account.interface_theme = interface_theme

        # Set timezone based on language
        account.timezone = language_timezone_mapping.get(interface_language, "UTC")

        db.session.add(account)
        db.session.commit()
        return account

    @staticmethod
    def create_account_and_tenant(
            email: str, name: str, interface_language: str, password: Optional[str] = None
    ) -> Account:
        """create account"""
        account = AccountService.create_account(
            email=email, name=name, interface_language=interface_language, password=password
        )

        TenantService.create_owner_tenant_if_not_exist(account=account)

        return account

    @staticmethod
    def generate_account_deletion_verification_code(account: Account) -> tuple[str, str]:
        code = "".join([str(random.randint(0, 9)) for _ in range(6)])
        token = TokenManager.generate_token(
            account=account, token_type="account_deletion", additional_data={"code": code}
        )
        return token, code

    @classmethod
    def send_account_deletion_verification_email(cls, account: Account, code: str):
        email = account.email
        if cls.email_code_account_deletion_rate_limiter.is_rate_limited(email):
            from controllers.console.auth.error import EmailCodeAccountDeletionRateLimitExceededError

            raise EmailCodeAccountDeletionRateLimitExceededError()

        send_account_deletion_verification_code.delay(to=email, code=code)

        cls.email_code_account_deletion_rate_limiter.increment_rate_limit(email)

    @staticmethod
    def verify_account_deletion_code(token: str, code: str) -> bool:
        token_data = TokenManager.get_token_data(token, "account_deletion")
        if token_data is None:
            return False

        if token_data["code"] != code:
            return False

        return True

    @staticmethod
    def delete_account(account: Account) -> None:
        """Delete account. This method only adds a task to the queue for deletion."""
        delete_account_task.delay(account.id)

    @staticmethod
    def link_account_integrate(provider: str, open_id: str, account: Account) -> None:
        """Link account integrate"""
        try:
            # Query whether there is an existing binding record for the same provider
            account_integrate: Optional[AccountIntegrate] = AccountIntegrate.query.filter_by(
                account_id=account.id, provider=provider
            ).first()

            if account_integrate:
                # If it exists, update the record
                account_integrate.open_id = open_id
                account_integrate.encrypted_token = ""  # todo
                account_integrate.updated_at = datetime.now(UTC).replace(tzinfo=None)
            else:
                # If it does not exist, create a new record
                account_integrate = AccountIntegrate(
                    account_id=account.id, provider=provider, open_id=open_id, encrypted_token=""
                )
                db.session.add(account_integrate)

            db.session.commit()
            logging.info(f"Account {account.id} linked {provider} account {open_id}.")
        except Exception as e:
            logging.exception(f"Failed to link {provider} account {open_id} to Account {account.id}")
            raise LinkAccountIntegrateError("Failed to link account.") from e

    @staticmethod
    def close_account(account: Account) -> None:
        """Close account"""
        account.status = AccountStatus.CLOSED.value
        db.session.commit()

    @staticmethod
    def update_account(account, **kwargs):
        """Update account fields"""
        for field, value in kwargs.items():
            if hasattr(account, field):
                setattr(account, field, value)
            else:
                raise AttributeError(f"Invalid field: {field}")

        db.session.commit()
        return account

    @staticmethod
    def update_login_info(account: Account, *, ip_address: str) -> None:
        """Update last login time and ip"""
        account.last_login_at = datetime.now(UTC).replace(tzinfo=None)
        account.last_login_ip = ip_address
        db.session.add(account)
        db.session.commit()

    @staticmethod
    def login(account: Account, *, ip_address: Optional[str] = None) -> TokenPair:
        if ip_address:
            AccountService.update_login_info(account=account, ip_address=ip_address)

        if account.status == AccountStatus.PENDING.value:
            account.status = AccountStatus.ACTIVE.value
            db.session.commit()

        access_token = AccountService.get_account_jwt_token(account=account)
        refresh_token = _generate_refresh_token()

        AccountService._store_refresh_token(refresh_token, account.id)

        return TokenPair(access_token=access_token, refresh_token=refresh_token)

    @staticmethod
    def logout(*, account: Account) -> None:
        refresh_token = redis_client.get(AccountService._get_account_refresh_token_key(account.id))
        if refresh_token:
            AccountService._delete_refresh_token(refresh_token.decode("utf-8"), account.id)

    @staticmethod
    def refresh_token(refresh_token: str) -> TokenPair:
        # Verify the refresh token
        account_id = redis_client.get(AccountService._get_refresh_token_key(refresh_token))
        if not account_id:
            raise ValueError("Invalid refresh token")

        account = AccountService.load_user(account_id.decode("utf-8"))
        if not account:
            raise ValueError("Invalid account")

        # Generate new access token and refresh token
        new_access_token = AccountService.get_account_jwt_token(account)
        new_refresh_token = _generate_refresh_token()

        AccountService._delete_refresh_token(refresh_token, account.id)
        AccountService._store_refresh_token(new_refresh_token, account.id)

        return TokenPair(access_token=new_access_token, refresh_token=new_refresh_token)

    @staticmethod
    def load_logged_in_account(*, account_id: str):
        return AccountService.load_user(account_id)

    @classmethod
    def send_reset_password_email(
            cls,
            account: Optional[Account] = None,
            email: Optional[str] = None,
            language: Optional[str] = "en-US",
    ):
        account_email = account.email if account else email
        if account_email is None:
            raise ValueError("Email must be provided.")

        if cls.reset_password_rate_limiter.is_rate_limited(account_email):
            from controllers.console.auth.error import PasswordResetRateLimitExceededError

            raise PasswordResetRateLimitExceededError()

        code = "".join([str(random.randint(0, 9)) for _ in range(6)])
        token = TokenManager.generate_token(
            account=account, email=email, token_type="reset_password", additional_data={"code": code}
        )
        send_reset_password_mail_task.delay(
            language=language,
            to=account_email,
            code=code,
        )
        cls.reset_password_rate_limiter.increment_rate_limit(account_email)
        return token

    @classmethod
    def revoke_reset_password_token(cls, token: str):
        TokenManager.revoke_token(token, "reset_password")

    @classmethod
    def get_reset_password_data(cls, token: str) -> Optional[dict[str, Any]]:
        return TokenManager.get_token_data(token, "reset_password")

    @classmethod
    def send_email_code_login_email(
            cls, account: Optional[Account] = None, email: Optional[str] = None, language: Optional[str] = "en-US"
    ):
        email = account.email if account else email
        if email is None:
            raise ValueError("Email must be provided.")
        if cls.email_code_login_rate_limiter.is_rate_limited(email):
            from controllers.console.auth.error import EmailCodeLoginRateLimitExceededError

            raise EmailCodeLoginRateLimitExceededError()

        code = "".join([str(random.randint(0, 9)) for _ in range(6)])
        token = TokenManager.generate_token(
            account=account, email=email, token_type="email_code_login", additional_data={"code": code}
        )
        send_email_code_login_mail_task.delay(
            language=language,
            to=account.email if account else email,
            code=code,
        )
        cls.email_code_login_rate_limiter.increment_rate_limit(email)
        return token

    @classmethod
    def get_email_code_login_data(cls, token: str) -> Optional[dict[str, Any]]:
        return TokenManager.get_token_data(token, "email_code_login")

    @classmethod
    def revoke_email_code_login_token(cls, token: str):
        TokenManager.revoke_token(token, "email_code_login")

    @classmethod
    def get_user_through_email(cls, email: str):
        if dify_config.BILLING_ENABLED and BillingService.is_email_in_freeze(email):
            raise AccountRegisterError(
                description=(
                    "This email account has been deleted within the past "
                    "30 days and is temporarily unavailable for new account registration"
                )
            )

        account = db.session.query(Account).filter(Account.email == email).first()
        if not account:
            return None

        if account.status == AccountStatus.BANNED.value:
            raise Unauthorized("Account is banned.")

        return account

    @staticmethod
    def add_login_error_rate_limit(email: str) -> None:
        key = f"login_error_rate_limit:{email}"
        count = redis_client.get(key)
        if count is None:
            count = 0
        count = int(count) + 1
        redis_client.setex(key, dify_config.LOGIN_LOCKOUT_DURATION, count)

    @staticmethod
    def is_login_error_rate_limit(email: str) -> bool:
        key = f"login_error_rate_limit:{email}"
        count = redis_client.get(key)
        if count is None:
            return False

        count = int(count)
        if count > AccountService.LOGIN_MAX_ERROR_LIMITS:
            return True
        return False

    @staticmethod
    def reset_login_error_rate_limit(email: str):
        key = f"login_error_rate_limit:{email}"
        redis_client.delete(key)

    @staticmethod
    def add_forgot_password_error_rate_limit(email: str) -> None:
        key = f"forgot_password_error_rate_limit:{email}"
        count = redis_client.get(key)
        if count is None:
            count = 0
        count = int(count) + 1
        redis_client.setex(key, dify_config.FORGOT_PASSWORD_LOCKOUT_DURATION, count)

    @staticmethod
    def is_forgot_password_error_rate_limit(email: str) -> bool:
        key = f"forgot_password_error_rate_limit:{email}"
        count = redis_client.get(key)
        if count is None:
            return False

        count = int(count)
        if count > AccountService.FORGOT_PASSWORD_MAX_ERROR_LIMITS:
            return True
        return False

    @staticmethod
    def reset_forgot_password_error_rate_limit(email: str):
        key = f"forgot_password_error_rate_limit:{email}"
        redis_client.delete(key)

    @staticmethod
    def is_email_send_ip_limit(ip_address: str):
        minute_key = f"email_send_ip_limit_minute:{ip_address}"
        freeze_key = f"email_send_ip_limit_freeze:{ip_address}"
        hour_limit_key = f"email_send_ip_limit_hour:{ip_address}"

        # check ip is frozen
        if redis_client.get(freeze_key):
            return True

        # check current minute count
        current_minute_count = redis_client.get(minute_key)
        if current_minute_count is None:
            current_minute_count = 0
        current_minute_count = int(current_minute_count)

        # check current hour count
        if current_minute_count > dify_config.EMAIL_SEND_IP_LIMIT_PER_MINUTE:
            hour_limit_count = redis_client.get(hour_limit_key)
            if hour_limit_count is None:
                hour_limit_count = 0
            hour_limit_count = int(hour_limit_count)

            if hour_limit_count >= 1:
                redis_client.setex(freeze_key, 60 * 60, 1)
                return True
            else:
                redis_client.setex(hour_limit_key, 60 * 10, hour_limit_count + 1)  # first time limit 10 minutes

            # add hour limit count
            redis_client.incr(hour_limit_key)
            redis_client.expire(hour_limit_key, 60 * 60)

            return True

        redis_client.setex(minute_key, 60, current_minute_count + 1)
        redis_client.expire(minute_key, 60)

        return False

    # 根据项目空间id查询下面用户
    @staticmethod
    def get_accounts_by_tenant_id_paginated(tenant_id: str, page: int, limit: int, filter: str = None):
        from models.account import Department
        from sqlalchemy import or_

        query = (
            db.session.query(
                Account.id.label("account_id"),
                Account.name.label("account_name"),
                Account.email.label("account_email"),
                Account.department_id,
                Department.name.label("department_name"),
                Department.id.label("department_id"),
                TenantAccountJoin.role,
            )
            .join(TenantAccountJoin, Account.id == TenantAccountJoin.account_id)
            .outerjoin(Department, Account.department_id == Department.id)
            .filter(TenantAccountJoin.tenant_id == tenant_id)
            .filter(Account.status == AccountStatus.ACTIVE.value)
            .order_by(TenantAccountJoin.created_at.desc())  # 按更新时间倒序
        )

        if filter:
            query = query.filter(
                or_(
                    Account.email.ilike(f"%{filter}%"),
                    Account.name.ilike(f"%{filter}%"),
                    Department.name.ilike(f"%{filter}%")
                )
            )

        paginated_result = query.paginate(page=page, per_page=limit, error_out=False)

        def get_department_full_path(dept_id):
            names = []
            while dept_id:
                dept = db.session.query(Department).filter(Department.id == dept_id).first()
                if not dept:
                    break
                names.append(dept.name)
                dept_id = dept.parent
            return "/".join(reversed(names)) if names else None

        return {
            "data": [
                {
                    "account_id": str(result.account_id),
                    "account_name": result.account_name,
                    "account_email": result.account_email,
                    "department_id": str(result.department_id) if result.department_id else None,
                    "department_name": get_department_full_path(result.department_id) if result.department_id else None,
                    "role": result.role,
                }
                for result in paginated_result.items
            ],
            "total": paginated_result.total,
            "page": paginated_result.page,
            "limit": paginated_result.per_page,
        }


def _get_login_cache_key(*, account_id: str, token: str):
    return f"account_login:{account_id}:{token}"


class TenantService:
    @staticmethod
    def create_tenant(name: str, adminUserIds: str, account_id: str, description: Optional[str] = None,
                      type: Optional[str] = None,
                      is_setup: Optional[bool] = False, is_from_dashboard: Optional[bool] = False) -> Tenant:
        """Create tenant"""
        if (
                not FeatureService.get_system_features().is_allow_create_workspace
                and not is_setup
                and not is_from_dashboard
        ):
            from controllers.console.error import NotAllowedCreateWorkspace

            raise NotAllowedCreateWorkspace()

        # 检查是否存在相同名称的 tenant
        existing_tenant = db.session.query(Tenant).filter_by(name=name).first()
        if existing_tenant:
            raise ValueError(f"项目空间名称：'{name}'已经存在.")

        # 如果 type 为 None，则设置为 "personal"
        type = type or "personal"
        # 获取当前登录用户的名称
        try:
            current_user_name = current_user.name if current_user and hasattr(current_user, "name") else "Unknown"
        except Exception:
            current_user_name = 'System'  # 跳转登录创建的账号设置为System

        tenant = Tenant(name=name, description=description, type=type, created_user=current_user_name)
        db.session.add(tenant)
        db.session.commit()
        tenant.encrypt_public_key = generate_key_pair(tenant.id)
        db.session.commit()

        # 为团队空间安装默认插件
        db.session.flush()
        if type == "public":
            PluginService.sync_builtin_plugins_for_tenant(tenant.id)
            # 初始化模型供应商，模型供应商模型
            try:
                model_provider_service = ModelProviderService()
                model_provider_service.init_all_provider_model_config_to_tenant(tenant.id)
            except Exception as e:
                # 模型配置错误后，需要清空插件，否则会有脏数据
                PluginService.clear_builtin_plugins_for_tenant(tenant.id)
                raise Exception(
                    "Failed to sync model provider configuration for the tenant. "
                    "Please check your model provider settings."
                ) from e

        # 如果 account_id 不为空且 type 为 "personal"，绑定 account_id 和 tenant
        if account_id and type == "personal":
            tenant_account_join = TenantAccountJoin(tenant_id=tenant.id, account_id=account_id, role="owner",
                                                    current=True)
            db.session.add(tenant_account_join)

        # 如果 adminUserIds 不为空，绑定 adminUserIds 和 tenant
        if adminUserIds:
            admin_ids = adminUserIds.split(",")  # 假设 adminUserIds 是逗号分隔的字符串
            for admin_id in admin_ids:
                tenant_account_join = TenantAccountJoin(tenant_id=tenant.id, account_id=admin_id, role="admin",
                                                        current=True)
                db.session.add(tenant_account_join)

        db.session.commit()
        return tenant

    @staticmethod
    def update_tenant(tenant_id: str, name: str, description: str, admin_user_ids: str) -> Tenant:
        # 查询项目空间
        tenant = db.session.query(Tenant).filter_by(id=tenant_id).first()
        if not tenant:
            raise ValueError(f"Tenant with ID '{tenant_id}' not found.")

        # 检查是否存在相同名称的其他租户
        existing_tenant = db.session.query(Tenant).filter(Tenant.name == name, Tenant.id != tenant_id).first()
        if existing_tenant:
            raise ValueError(f"Tenant name '{name}' is already in use.")

        # 更新项目空间的名称和描述
        tenant.name = name
        tenant.description = description

        # 获取当前关联的管理员用户
        current_admin_joins = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, role="admin").all()
        current_admin_ids = {join.account_id for join in current_admin_joins}

        # 解析传入的 adminUserIds
        new_admin_ids = set(admin_user_ids.split(","))

        # 找出需要删除的管理员
        admin_ids_to_remove = current_admin_ids - new_admin_ids
        for account_id in admin_ids_to_remove:
            join = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, account_id=account_id, role="admin").first()
            if join:
                db.session.delete(join)

        # 找出需要新增的管理员
        admin_ids_to_add = new_admin_ids - current_admin_ids
        for account_id in admin_ids_to_add:
            new_join = TenantAccountJoin(tenant_id=tenant_id, account_id=account_id, role="admin", current=True)
            db.session.add(new_join)

        # 提交更改
        db.session.commit()
        return tenant

    @staticmethod
    def update_tenant_status(tenant_id: str, status: str) -> Tenant:
        # 验证 status 是否为合法值
        if status not in ["normal", "archive"]:
            raise ValueError("Invalid status value. Allowed values are 'normal' and 'archive'.")

        # 查询租户
        tenant = db.session.query(Tenant).filter_by(id=tenant_id).first()
        if not tenant:
            raise ValueError(f"Tenant with ID '{tenant_id}' not found.")

        # 更新状态
        tenant.status = status
        db.session.commit()
        return tenant

    @staticmethod
    def get_public_tenants_paginated(page: int, limit: int, filter: str = None):
        query = (
            db.session.query(
                Tenant.id,
                Tenant.name,
                Tenant.description,
                Tenant.status,
                Tenant.created_at,
                db.func.count(TenantAccountJoin.id).label("member_count"),
                db.func.string_agg(
                    db.case(
                        (TenantAccountJoin.role == "admin", Account.email),
                        else_=None
                    ),
                    ","
                ).label("admin_names")
            )
            .join(TenantAccountJoin, Tenant.id == TenantAccountJoin.tenant_id)
            .join(Account, TenantAccountJoin.account_id == Account.id)
            .filter(Tenant.type == "public", Tenant.hide == False)
            .filter(Account.status == AccountStatus.ACTIVE.value)  # 只统计启用用户
            .group_by(Tenant.id)
            .order_by(Tenant.created_at.desc())
        )

        # 添加模糊查询条件
        from sqlalchemy import or_

        if filter:
            agg_expr = db.func.string_agg(
                db.case(
                    (TenantAccountJoin.role == "admin", Account.email),
                    else_=None
                ),
                ","
            )
            query = query.having(
                or_(
                    Tenant.name.ilike(f"%{filter}%"),
                    Tenant.description.ilike(f"%{filter}%"),
                    agg_expr.ilike(f"%{filter}%")
                )
            )

        paginated_result = query.paginate(page=page, per_page=limit, error_out=False)

        return paginated_result.items, paginated_result.total

    @staticmethod
    def get_public_tenants(filter: str = None):
        query = (
            db.session.query(
                Tenant.id,
                Tenant.name,
                Tenant.description,
                Tenant.status,
                Tenant.created_at,
                db.func.count(TenantAccountJoin.id).label("member_count"),
                db.func.string_agg(
                    db.case(
                        (TenantAccountJoin.role == "admin", Account.email),
                        else_=None
                    ),
                    ","
                ).label("admin_names")
            )
            .join(TenantAccountJoin, Tenant.id == TenantAccountJoin.tenant_id)
            .join(Account, TenantAccountJoin.account_id == Account.id)
            .filter(Tenant.type == "public", Tenant.hide == False)
            .filter(Account.status == AccountStatus.ACTIVE.value)  # 只统计启用用户
            .group_by(Tenant.id)
            .order_by(Tenant.created_at.desc())
        )

        # 添加模糊查询条件
        from sqlalchemy import or_

        if filter:
            agg_expr = db.func.string_agg(
                db.case(
                    (TenantAccountJoin.role == "admin", Account.email),
                    else_=None
                ),
                ","
            )
            query = query.having(
                or_(
                    Tenant.name.ilike(f"%{filter}%"),
                    Tenant.description.ilike(f"%{filter}%"),
                    agg_expr.ilike(f"%{filter}%")
                )
            )

        return query.all()

    @staticmethod
    def create_owner_tenant_if_not_exist(
            account: Account, name: Optional[str] = None, is_setup: Optional[bool] = False
    ):
        """Check if user have a workspace or not"""
        available_ta = (
            TenantAccountJoin.query.filter_by(account_id=account.id).order_by(TenantAccountJoin.id.asc()).first()
        )

        if available_ta:
            return

        """Create owner tenant if not exist"""
        if not FeatureService.get_system_features().is_allow_create_workspace and not is_setup:
            raise WorkSpaceNotAllowedCreateError()

        if name:
            tenant = TenantService.create_tenant(name=name, is_setup=is_setup)
        else:
            tenant = TenantService.create_tenant(name=f"{account.name}'s Workspace", is_setup=is_setup)
        TenantService.create_tenant_member(tenant, account, role="owner")
        account.current_tenant = tenant
        db.session.commit()
        tenant_was_created.send(tenant)

    @staticmethod
    def create_owner_tenant_with_master_if_not_exist(
            account: Account, master_account: Optional[Account] = None, name: Optional[str] = None,
            is_setup: Optional[bool] = False
    ):
        """Check if user have a workspace or not"""
        available_ta = (
            TenantAccountJoin.query.filter_by(account_id=account.id).order_by(TenantAccountJoin.id.asc()).first()
        )

        if available_ta:
            return

        """Create owner tenant if not exist"""
        if not FeatureService.get_system_features().is_allow_create_workspace and not is_setup:
            raise WorkSpaceNotAllowedCreateError()

        if name:
            tenant = TenantService.create_tenant(name=name, is_setup=is_setup)
        else:
            tenant = TenantService.create_tenant(name=f"{account.name}'s Workspace", is_setup=is_setup)
        TenantService.create_tenant_member(tenant, account, role="owner")
        # 多租户场景，绑定 master 账号
        if account.email != "<EMAIL>":
            logging.info("create master member ,master_account is exist")
            TenantService.create_tenant_member(tenant, master_account, role=TenantAccountRole.ADMIN.value)
        account.current_tenant = tenant
        db.session.commit()
        tenant_was_created.send(tenant)

    @staticmethod
    def create_tenant_member(tenant: Tenant, account: Account, role: str = "normal") -> TenantAccountJoin:
        """Create tenant member"""
        if role == TenantAccountRole.OWNER.value:
            if TenantService.has_roles(tenant, [TenantAccountRole.OWNER]):
                logging.error(f"Tenant {tenant.id} has already an owner.")
                raise Exception("Tenant already has an owner.")

        ta = db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id, account_id=account.id).first()
        if ta:
            ta.role = role
        else:
            ta = TenantAccountJoin(tenant_id=tenant.id, account_id=account.id, role=role)
            db.session.add(ta)

        db.session.commit()
        return ta

    @staticmethod
    def get_join_tenants(account: Account) -> list[Tenant]:
        """Get account join tenants"""
        return (
            db.session.query(Tenant)
            .join(TenantAccountJoin, Tenant.id == TenantAccountJoin.tenant_id)
            .filter(TenantAccountJoin.account_id == account.id, Tenant.status == TenantStatus.NORMAL)
            .all()
        )

    @staticmethod
    def get_public_join_tenants(account: Account) -> list[Tenant]:
        """Get account join public tenants"""

        tenants = (
            db.session.query(Tenant)
            .join(TenantAccountJoin, Tenant.id == TenantAccountJoin.tenant_id)
            .filter(
                TenantAccountJoin.account_id == account.id,
                Tenant.status == TenantStatus.NORMAL,
                Tenant.type.in_(["public", "official"]),
            )
            .all()
        )

        if not tenants:
            return []

        has_current = False
        for tenant in tenants:
            if tenant.id == account.current_tenant_id:
                has_current = True
        if not has_current:
            TenantService.switch_tenant(account, tenants[0].id)

        return tenants

    @staticmethod
    def get_current_tenant_by_account(account: Account):
        """Get tenant by account and add the role"""
        tenant = account.current_tenant
        if not tenant:
            raise TenantNotFoundError("Tenant not found.")

        ta = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=account.id).first()
        if ta:
            tenant.role = ta.role
        else:
            raise TenantNotFoundError("Tenant not found for the account.")
        return tenant

    @staticmethod
    def get_tenant_by_account_email(account: Account):
        """Get tenant by account and add the role"""
        ta = TenantAccountJoin.query.filter_by(role=TenantAccountRole.OWNER, account_id=account.id).first()
        tenant = Tenant.query.filter_by(id=ta.tenant_id).first()
        return tenant

    @staticmethod
    def switch_tenant(account: Account, tenant_id: Optional[str] = None) -> None:
        """Switch the current workspace for the account"""

        # Ensure tenant_id is provided
        if tenant_id is None:
            raise ValueError("Tenant ID must be provided.")

        tenant_account_join = (
            db.session.query(TenantAccountJoin)
            .join(Tenant, TenantAccountJoin.tenant_id == Tenant.id)
            .filter(
                TenantAccountJoin.account_id == account.id,
                TenantAccountJoin.tenant_id == tenant_id,
                Tenant.status == TenantStatus.NORMAL,
            )
            .first()
        )

        if not tenant_account_join:
            raise AccountNotLinkTenantError("Tenant not found or account is not a member of the tenant.")
        else:
            TenantAccountJoin.query.filter(
                TenantAccountJoin.account_id == account.id, TenantAccountJoin.tenant_id != tenant_id
            ).update({"current": False})
            tenant_account_join.current = True
            # Set the current tenant for the account
            account.current_tenant_id = tenant_account_join.tenant_id
            db.session.commit()

    @staticmethod
    def get_tenant_members(tenant: Tenant) -> list[Account]:
        """Get tenant members"""
        query = (
            db.session.query(Account, TenantAccountJoin.role)
            .select_from(Account)
            .join(TenantAccountJoin, Account.id == TenantAccountJoin.account_id)
            .filter(TenantAccountJoin.tenant_id == tenant.id)
            .filter(Account.status == AccountStatus.ACTIVE.value)
        )

        # Initialize an empty list to store the updated accounts
        updated_accounts = []

        for account, role in query:
            account.role = role
            if account.email == '<EMAIL>' and role == 'admin':
                continue
            updated_accounts.append(account)

        return updated_accounts

    @staticmethod
    def get_dataset_operator_members(tenant: Tenant) -> list[Account]:
        """Get dataset admin members"""
        query = (
            db.session.query(Account, TenantAccountJoin.role)
            .select_from(Account)
            .join(TenantAccountJoin, Account.id == TenantAccountJoin.account_id)
            .filter(TenantAccountJoin.tenant_id == tenant.id)
            .filter(TenantAccountJoin.role == "dataset_operator")
        )

        # Initialize an empty list to store the updated accounts
        updated_accounts = []

        for account, role in query:
            account.role = role
            updated_accounts.append(account)

        return updated_accounts

    @staticmethod
    def has_roles(tenant: Tenant, roles: list[TenantAccountRole]) -> bool:
        """Check if user has any of the given roles for a tenant"""
        if not all(isinstance(role, TenantAccountRole) for role in roles):
            raise ValueError("all roles must be TenantAccountRole")

        return (
                db.session.query(TenantAccountJoin)
                .filter(
                    TenantAccountJoin.tenant_id == tenant.id, TenantAccountJoin.role.in_([role.value for role in roles])
                )
                .first()
                is not None
        )

    @staticmethod
    def get_user_role(account: Account, tenant: Tenant) -> Optional[TenantAccountRole]:
        """Get the role of the current account for a given tenant"""
        join = (
            db.session.query(TenantAccountJoin)
            .filter(TenantAccountJoin.tenant_id == tenant.id, TenantAccountJoin.account_id == account.id)
            .first()
        )
        return join.role if join else None

    @staticmethod
    def get_tenant_count() -> int:
        """Get tenant count"""
        return cast(int, db.session.query(func.count(Tenant.id)).scalar())

    @staticmethod
    def check_member_permission(tenant: Tenant, operator: Account, member: Account | None, action: str) -> None:
        """Check member permission"""
        perms = {
            "add": [TenantAccountRole.OWNER, TenantAccountRole.ADMIN],
            "remove": [TenantAccountRole.OWNER],
            "update": [TenantAccountRole.OWNER],
        }
        if action not in {"add", "remove", "update"}:
            raise InvalidActionError("Invalid action.")

        if member:
            if operator.id == member.id:
                raise CannotOperateSelfError("Cannot operate self.")

        ta_operator = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=operator.id).first()

        if not ta_operator or ta_operator.role not in perms[action]:
            raise NoPermissionError(f"No permission to {action} member.")

    @staticmethod
    def remove_member_from_tenant(tenant: Tenant, account: Account, operator: Account) -> None:
        """Remove member from tenant"""
        if operator.id == account.id:
            raise CannotOperateSelfError("Cannot operate self.")

        TenantService.check_member_permission(tenant, operator, account, "remove")

        ta = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=account.id).first()
        if not ta:
            raise MemberNotInTenantError("Member not in tenant.")

        db.session.delete(ta)
        db.session.commit()

    @staticmethod
    def update_member_role(tenant: Tenant, member: Account, new_role: str, operator: Account) -> None:
        """Update member role"""
        TenantService.check_member_permission(tenant, operator, member, "update")

        target_member_join = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=member.id).first()

        if target_member_join.role == new_role:
            raise RoleAlreadyAssignedError("The provided role is already assigned to the member.")

        if new_role == "owner":
            # Find the current owner and change their role to 'admin'
            current_owner_join = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, role="owner").first()
            current_owner_join.role = "admin"

        # Update the role of the target member
        target_member_join.role = new_role
        db.session.commit()

    @staticmethod
    def dissolve_tenant(tenant: Tenant, operator: Account) -> None:
        """Dissolve tenant"""
        if not TenantService.check_member_permission(tenant, operator, operator, "remove"):
            raise NoPermissionError("No permission to dissolve tenant.")
        db.session.query(TenantAccountJoin).filter_by(tenant_id=tenant.id).delete()
        db.session.delete(tenant)
        db.session.commit()

    @staticmethod
    def get_custom_config(tenant_id: str) -> dict:
        tenant = Tenant.query.filter(Tenant.id == tenant_id).one_or_404()

        return cast(dict, tenant.custom_config_dict)

    @staticmethod
    def add_tenant_members(tenant_id: str, user_ids: list[str], role: str) -> None:
        """向项目空间添加成员"""
        for user_id in user_ids:
            # 检查是否已存在
            existing_member = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, account_id=user_id).first()
            if existing_member:
                existing_member.role = role  # 更新角色
            else:
                # 插入新成员
                new_member = TenantAccountJoin(tenant_id=tenant_id, account_id=user_id, role=role, current=True)
                db.session.add(new_member)

        db.session.commit()

    @staticmethod
    def remove_tenant_member(tenant_id: str, user_id: str) -> None:
        """移除项目空间成员"""
        # 查询当前项目空间中所有管理员的数量
        admin_count = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, role="admin").count()

        # 检查是否试图移除最后一个管理员
        member_to_remove = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, account_id=user_id).first()
        if not member_to_remove:
            raise ValueError("Member not found in the tenant.")

        if member_to_remove.role == "admin" and admin_count <= 1:
            raise ValueError("Cannot remove the last admin of the tenant.")

        # 删除成员
        db.session.delete(member_to_remove)
        db.session.commit()

    @staticmethod
    def update_tenant_member(tenant_id: str, user_id: str, role: str) -> None:
        """编辑项目空间成员的角色"""
        member = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, account_id=user_id).first()
        if not member:
            raise ValueError("Member not found in the tenant.")

        # 检查是否试图将最后一个管理员降级为非管理员
        if member.role == "admin" and role != "admin":
            admin_count = TenantAccountJoin.query.filter_by(tenant_id=tenant_id, role="admin").count()
            if admin_count <= 1:
                raise ValueError("Cannot remove the last admin of the tenant.")

        member.role = role
        db.session.commit()

    @staticmethod
    def query_tenant_members(tenant_id: str | None, action: str, email: str = None) -> list[dict]:
        """
        查询项目空间成员下拉框
        :param tenant_id: 项目空间 ID，可选
        :param action: 操作类型，addMember:添加成员   editTenant:编辑项目空间   addTenant:添加项目空间
        :return: 用户列表
        """
        query = db.session.query(Account.id, Account.name, Account.email).filter(
            Account.status == AccountStatus.ACTIVE.value
        )

        if email:
            query = query.filter(Account.email.ilike(f"%{email}%"))

        if action == "addMember":
            # 查询不存在于项目空间的用户
            if not tenant_id:
                raise ValueError("tenant_id is required for 'addMember' action.")
            query = query.filter(
                ~Account.id.in_(
                    db.session.query(TenantAccountJoin.account_id).filter_by(tenant_id=tenant_id)
                )
            )
        elif action == "editTenant":
            if not tenant_id:
                raise ValueError("tenant_id is required for 'editTenant' action.")

            exists_case = case(
                (TenantAccountJoin.account_id != None, True),
                else_=False
            ).label("exists")

            query = query.outerjoin(
                TenantAccountJoin,
                (Account.id == TenantAccountJoin.account_id) & (TenantAccountJoin.tenant_id == tenant_id)
            ).add_columns(exists_case)
        elif action == "addTenant":
            # 查询所有用户，无需过滤
            pass
        else:
            raise ValueError("Invalid action type.")

        result = query.all()
        user_list = []
        for row in result:
            if action == "editTenant":
                # row 是一个 tuple：(id, name, email, exists)
                user_id, name, email, exists = row
            else:
                user_id, name, email = row
                exists = None

            user_list.append({
                "id": str(user_id),
                "email": email,
                "exists": exists
            })

        return user_list

    # 查询项目空间已经加入的人员（editTenant：查询已加入管理员  addMember：查询已加入人员）
    @staticmethod
    def query_tenant_members_has_joined(tenant_id: str | None, action: str):
        if not tenant_id or action == "addTenant":
            return []

        query = db.session.query(Account).join(TenantAccountJoin, Account.id == TenantAccountJoin.account_id) \
            .filter(TenantAccountJoin.tenant_id == tenant_id).filter(
            Account.status == AccountStatus.ACTIVE.value)  # 新增状态过滤

        if action == "editTenant":
            query = query.filter(TenantAccountJoin.role == "admin")

        members = query.all()
        user_list = []
        for member in members:
            user_list.append({
                "id": str(member.id),
                "email": member.email
            })
        return user_list


class RegisterService:
    @classmethod
    def _get_invitation_token_key(cls, token: str) -> str:
        return f"member_invite:token:{token}"

    @classmethod
    def setup(cls, email: str, name: str, password: str, ip_address: str) -> None:
        """
        Setup dify

        :param email: email
        :param name: username
        :param password: password
        :param ip_address: ip address
        """
        try:
            # Register
            account = AccountService.create_account(
                email=email,
                name=name,
                interface_language=languages[0],
                password=password,
                is_setup=True,
            )

            account.last_login_ip = ip_address
            account.initialized_at = datetime.now(UTC).replace(tzinfo=None)

            TenantService.create_owner_tenant_if_not_exist(account=account, is_setup=True)

            dify_setup = DifySetup(version=dify_config.CURRENT_VERSION)
            db.session.add(dify_setup)
            db.session.commit()
        except Exception as e:
            db.session.query(DifySetup).delete()
            db.session.query(TenantAccountJoin).delete()
            db.session.query(Account).delete()
            db.session.query(Tenant).delete()
            db.session.commit()

            logging.exception(f"Setup account failed, email: {email}, name: {name}")
            raise ValueError(f"Setup failed: {e}")

    @classmethod
    def register(
            cls,
            email,
            name,
            password: Optional[str] = None,
            open_id: Optional[str] = None,
            provider: Optional[str] = None,
            language: Optional[str] = None,
            status: Optional[AccountStatus] = None,
            is_setup: Optional[bool] = False,
            create_workspace_required: Optional[bool] = True,
    ) -> Account:
        db.session.begin_nested()
        """Register account"""
        try:
            account = AccountService.create_account(
                email=email,
                name=name,
                interface_language=language or languages[0],
                password=password,
                is_setup=is_setup,
            )
            account.status = AccountStatus.ACTIVE.value if not status else status.value
            account.initialized_at = datetime.now(UTC).replace(tzinfo=None)

            if open_id is not None and provider is not None:
                AccountService.link_account_integrate(provider, open_id, account)

            if FeatureService.get_system_features().is_allow_create_workspace and create_workspace_required:
                tenant = TenantService.create_tenant(f"{account.name}'s Workspace")
                TenantService.create_tenant_member(tenant, account, role="owner")
                account.current_tenant = tenant
                tenant_was_created.send(tenant)

            db.session.commit()
        except WorkSpaceNotAllowedCreateError:
            db.session.rollback()
            logging.exception("Register failed")
            raise AccountRegisterError("Workspace is not allowed to create.")
        except AccountRegisterError as are:
            db.session.rollback()
            logging.exception("Register failed")
            raise are
        except Exception as e:
            db.session.rollback()
            logging.exception("Register failed")
            raise AccountRegisterError(f"Registration failed: {e}") from e

        return account

    @classmethod
    def invite_new_member(
            cls, tenant: Tenant, email: str, language: str, role: str = "normal", inviter: Account | None = None
    ) -> str:
        if not inviter:
            raise ValueError("Inviter is required")

        """Invite new member"""
        with Session(db.engine) as session:
            account = session.query(Account).filter_by(email=email).first()

        if not account:
            TenantService.check_member_permission(tenant, inviter, None, "add")
            name = email.split("@")[0]

            account = cls.register(
                email=email, name=name, language=language, status=AccountStatus.PENDING, is_setup=True
            )
            # Create new tenant member for invited tenant
            TenantService.create_tenant_member(tenant, account, role)
            TenantService.switch_tenant(account, tenant.id)
        else:
            TenantService.check_member_permission(tenant, inviter, account, "add")
            ta = TenantAccountJoin.query.filter_by(tenant_id=tenant.id, account_id=account.id).first()

            if not ta:
                TenantService.create_tenant_member(tenant, account, role)

            # Support resend invitation email when the account is pending status
            if account.status != AccountStatus.PENDING.value:
                raise AccountAlreadyInTenantError("Account already in tenant.")

        token = cls.generate_invite_token(tenant, account)

        # send email
        send_invite_member_mail_task.delay(
            language=account.interface_language,
            to=email,
            token=token,
            inviter_name=inviter.name if inviter else "Dify",
            workspace_name=tenant.name,
        )

        return token

    @classmethod
    def generate_invite_token(cls, tenant: Tenant, account: Account) -> str:
        token = str(uuid.uuid4())
        invitation_data = {
            "account_id": account.id,
            "email": account.email,
            "workspace_id": tenant.id,
        }
        expiry_hours = dify_config.INVITE_EXPIRY_HOURS
        redis_client.setex(cls._get_invitation_token_key(token), expiry_hours * 60 * 60, json.dumps(invitation_data))
        return token

    @classmethod
    def is_valid_invite_token(cls, token: str) -> bool:
        data = redis_client.get(cls._get_invitation_token_key(token))
        return data is not None

    @classmethod
    def revoke_token(cls, workspace_id: str, email: str, token: str):
        if workspace_id and email:
            email_hash = sha256(email.encode()).hexdigest()
            cache_key = "member_invite_token:{}, {}:{}".format(workspace_id, email_hash, token)
            redis_client.delete(cache_key)
        else:
            redis_client.delete(cls._get_invitation_token_key(token))

    @classmethod
    def get_invitation_if_token_valid(
            cls, workspace_id: Optional[str], email: str, token: str
    ) -> Optional[dict[str, Any]]:
        invitation_data = cls._get_invitation_by_token(token, workspace_id, email)
        if not invitation_data:
            return None

        tenant = (
            db.session.query(Tenant)
            .filter(Tenant.id == invitation_data["workspace_id"], Tenant.status == "normal")
            .first()
        )

        if not tenant:
            return None

        tenant_account = (
            db.session.query(Account, TenantAccountJoin.role)
            .join(TenantAccountJoin, Account.id == TenantAccountJoin.account_id)
            .filter(Account.email == invitation_data["email"], TenantAccountJoin.tenant_id == tenant.id)
            .first()
        )

        if not tenant_account:
            return None

        account = tenant_account[0]
        if not account:
            return None

        if invitation_data["account_id"] != str(account.id):
            return None

        return {
            "account": account,
            "data": invitation_data,
            "tenant": tenant,
        }

    @classmethod
    def _get_invitation_by_token(
            cls, token: str, workspace_id: Optional[str] = None, email: Optional[str] = None
    ) -> Optional[dict[str, str]]:
        if workspace_id is not None and email is not None:
            email_hash = sha256(email.encode()).hexdigest()
            cache_key = f"member_invite_token:{workspace_id}, {email_hash}:{token}"
            account_id = redis_client.get(cache_key)

            if not account_id:
                return None

            return {
                "account_id": account_id.decode("utf-8"),
                "email": email,
                "workspace_id": workspace_id,
            }
        else:
            data = redis_client.get(cls._get_invitation_token_key(token))
            if not data:
                return None

            invitation: dict = json.loads(data)
            return invitation


def _generate_refresh_token(length: int = 64):
    token = secrets.token_hex(length)
    return token
