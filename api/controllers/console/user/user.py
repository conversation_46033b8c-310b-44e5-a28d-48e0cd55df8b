import logging

# from flasgger import swag_from
from flask import request
from flask_restful import Resource, reqparse, marshal

from controllers.console.wraps import account_initialization_required, setup_required
from fields.user_fields import user_detail_fields
from libs.login import login_required
from services.user_service import UserService
# from yml.index import yml_dir


class UsersApi(Resource):
    # @swag_from(yml_dir + 'user_list.yml')
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        # logging.info(f"getUsers")
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        name = request.args.get("filter", default="", type=str)
        users, total = UserService.getUsers(page, limit, name)
        response = {"data": [marshal(item, user_detail_fields) for item in users], "total": total}
        return response
        # return {"result": "success"}

    @setup_required
    @login_required
    @account_initialization_required
    # @swag_from(yml_dir + 'user_add.yml')
    def post(self):
        logging.info(f"postUsers")
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            type=str,
            nullable=False,
            required=True,
            help="name is required. Name must be between 1 to 40 characters.",
        )
        parser.add_argument(
            "age",
            type=int,
            nullable=False,
            required=True,
            help="name is required. Name must be between 1 to 40 characters.",
        )
        args = parser.parse_args()
        UserService.saveUser(args)
        return {"result": "success"}


class UserApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    # @swag_from(yml_dir + 'user_get.yml')
    def get(self, user_id):
        user_id = str(user_id)
        user = UserService.getUser(user_id)
        response = {"data": marshal(user, user_detail_fields)}
        return response

    @setup_required
    @login_required
    @account_initialization_required
    # @swag_from(yml_dir + 'user_update.yml')
    def put(self, user_id):
        user_id = str(user_id)
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            type=str,
            nullable=False,
            required=True,
            help="name is required. Name must be between 1 to 40 characters.",
        )
        parser.add_argument(
            "age",
            type=int,
            nullable=False,
            required=True,
            help="name is required. Name must be between 1 to 40 characters.",
        )
        args = parser.parse_args()
        UserService.modifyUser(user_id, args)
        return {"result": "success"}

    @setup_required
    @login_required
    @account_initialization_required
    # @swag_from(yml_dir + 'user_delete.yml')
    def delete(self, user_id):
        user_id = str(user_id)
        UserService.deleteUser(user_id)
        return {"result": "success"}
        # try:
        #     if UserService.deleteUser(user_id):
        #         return {"result": "success"}, 204
        #     else:
        #         raise NotFound("User not found.")
        # except:
        #     raise Forbidden("user_in_use")
