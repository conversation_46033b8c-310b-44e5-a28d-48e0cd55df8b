from libs.exception import BaseHTTPException


class ApiKeyAuthFailedError(BaseHTTPException):
    error_code = "auth_failed"
    description = "{message}"
    code = 500


class InvalidEmailError(BaseHTTPException):
    error_code = "invalid_email"
    description = "电子邮件地址无效."#The email address is not valid
    code = 400


class PasswordMismatchError(BaseHTTPException):
    error_code = "password_mismatch"
    description = "密码不匹配."#The passwords do not match
    code = 400


class InvalidTokenError(BaseHTTPException):
    error_code = "invalid_or_expired_token"
    description = "令牌无效或已过期."#The token is invalid or has expired
    code = 400


class PasswordResetRateLimitExceededError(BaseHTTPException):
    error_code = "password_reset_rate_limit_exceeded"
    description = "已发送太多密码重置电子邮件。请在1分钟后重试."#Too many password reset emails have been sent. Please try again in 1 minutes
    code = 429


class EmailCodeError(BaseHTTPException):
    error_code = "email_code_error"
    description = "电子邮件代码无效或已过期."#Email code is invalid or expired
    code = 400


class EmailOrPasswordMismatchError(BaseHTTPException):
    error_code = "email_or_password_mismatch"
    description = "错误的账号或密码."#The email or password is mismatched
    code = 400


class EmailPasswordLoginLimitError(BaseHTTPException):
    error_code = "email_code_login_limit"
    description = "密码尝试错误次数过多。请稍后再试."#Too many incorrect password attempts. Please try again later
    code = 429


class EmailCodeLoginRateLimitExceededError(BaseHTTPException):
    error_code = "email_code_login_rate_limit_exceeded"
    description = "发送的登录电子邮件太多。请在5分钟后重试."#Too many login emails have been sent. Please try again in 5 minutes
    code = 429


class EmailCodeAccountDeletionRateLimitExceededError(BaseHTTPException):
    error_code = "email_code_account_deletion_rate_limit_exceeded"
    description = "发送的帐户删除电子邮件太多。请在5分钟后重试."#Too many account deletion emails have been sent. Please try again in 5 minutes
    code = 429


class EmailPasswordResetLimitError(BaseHTTPException):
    error_code = "email_password_reset_limit"
    description = "密码重置尝试失败次数过多。请在24小时后重试."#Too many failed password reset attempts. Please try again in 24 hours
    code = 429
