from flask_restful import Resource, marshal
from flask import request, jsonify

from controllers.console import api
from controllers.console.wraps import setup_required
from fields.privilege_fields import (
    privilege_fields,
)
from libs.login import login_required
from services.esage.privilege_service import PrivilegeService

privilege_service = PrivilegeService()

class PrivilegeListApi(Resource):
    @setup_required
    @login_required
    def get(self):
        """
        分页查询权限（privilege）列表
        """
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=20, type=int)
        name = request.args.get("filter", default="", type=str)
        is_enabled_str = request.args.get("is_enabled", default=None, type=str)
        if is_enabled_str is not None:
            is_enabled = is_enabled_str.lower() in ["true", "1", "t", "y", "yes"]
        else:
            is_enabled = None
        privileges, total = PrivilegeService.get_privileges_pages(page, limit, name, is_enabled)
        response = {"data": [marshal(item, privilege_fields) for item in privileges], "total": total}
        return response

class PrivilegeTreeApi(Resource):
    @setup_required
    @login_required
    def get(self):
        tree_privileges = PrivilegeService.get_tree_privileges()
        # 将树形结构转换为字典列表
        tree_dict = [root.to_dict() for root in tree_privileges]
        # 返回 JSON 响应
        return jsonify({
            'privileges': tree_dict,
        })


# 菜单列表
api.add_resource(PrivilegeListApi, "/privileges")
api.add_resource(PrivilegeTreeApi, "/privileges-tree")

