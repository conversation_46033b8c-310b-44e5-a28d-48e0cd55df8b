from flask import request, Response
from flask_restful import Resource, reqparse  # type: ignore

from controllers.console import api
from controllers.console.wraps import setup_required
from controllers.service_api.app.error import FileTooLargeError, UnsupportedFileTypeError
from services.file_service import FileService
from libs.login import login_required
import urllib


class ConvertFileApi(Resource):
    @setup_required
    @login_required
    def post(self):
        """
        Convert a file to PDF format from the provided file content.
        The file content should be provided as a string in the request body.
        The request should include the file name, file type, and file content.
        Returns the converted PDF file as a response.
        """
        parser = reqparse.RequestParser()
        parser.add_argument(
            "file_name", type=str, required=True, help="file_name is required."
        )
        parser.add_argument(
            "file_type",
            type=str,
            required=False,
            default="html",
            help="file_type is required.",
        )
        parser.add_argument(
            "file_content", type=str, required=True, help="file_content is required."
        )
        args = parser.parse_args()

        if not args.file_name:
            return {"message": "file_name is required."}, 403

        if not args.file_content:
            return {"message": "file_content is required."}, 403

        # 检查是否为 HTML 文件
        if args.file_type.lower() not in ["html", "htm"]:
            return {
                "message": "Only HTML files are supported for conversion to PDF."
            }, 403

        # 将字符串内容转换为字节
        if isinstance(args.file_content, str):
            content_bytes = args.file_content.encode("utf-8")
        else:
            content_bytes = args.file_content

        # 确定 MIME 类型
        mimetype = "text/html"

        # 确保文件名有正确的扩展名
        if not args.file_name.lower().endswith((".html", ".htm")):
            args.file_name = args.file_name + ".html"

        pdf_content = FileService.convert_file(
            filename=args.file_name,
            content=content_bytes,
            mimetype=mimetype,
        )

        # 生成 PDF 文件名
        pdf_filename = (
            args.file_name.rsplit(".", 1)[0] + ".pdf"
            if "." in args.file_name
            else args.file_name + ".pdf"
        )
        encoded_filename = urllib.parse.quote(pdf_filename.encode('utf-8'))

        # 直接返回 PDF 文件作为响应
        return Response(
            pdf_content,
            direct_passthrough=True,
            mimetype="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
                "Content-Length": str(len(pdf_content)),
                "Content-Type": "application/pdf",
            },
        )


api.add_resource(ConvertFileApi, "/files/convert/to-pdf")
