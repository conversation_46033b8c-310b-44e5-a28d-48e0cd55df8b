import logging
from flask_restful import Resource, fields, marshal, marshal_with, reqparse
from flask import request
from sqlalchemy.orm import Session
from werkzeug.exceptions import BadRequest, Forbidden
from controllers.console import api
from controllers.console.wraps import privilege_required, setup_required, account_initialization_required, hasPrivilege
from extensions.ext_database import db
from fields.app_extension_fields import (
    app_extension_fields,
    configs_fields,
    category_fields,
    app_extension_list_response_fields,
    app_accessible_response_fields,
)
from fields.app_fields import (
    app_partial_fields,
)
from configs import dify_config
from libs.login import current_user, login_required
from libs.swagger_decorators import swagger_api
from models.account import Account, RolePrivilegeType, RolePrivilege, AccountRole
from services.esage.app_extension_service import AppExtensionService
from models.model import InstalledApp

app_extension_service = AppExtensionService()

logger = logging.getLogger(__name__)
class AppExtensionPublishApi(Resource):

    @setup_required
    @login_required
    @marshal_with({"result": fields.String, "items": fields.List(fields.Nested(app_extension_fields))})
    def post(self, app_id):
        """
        发布应用为不同的场景（批量创建 app_extension）
        先删除该 app_id 下所有扩展，再插入新扩展
        """
        if not current_user.is_editor:
            raise Forbidden()

        parser = reqparse.RequestParser()
        parser.add_argument("type", type=str, required=True, choices=["builtin", "normal"], location="json")
        parser.add_argument("category_ids", type=list, required=True, location="json")
        parser.add_argument("description", type=str, required=False, location="json")
        args = parser.parse_args()

        app_id = str(app_id)
        category_ids = args["category_ids"]
        type_ = args["type"]
        description = args.get("description")

        if not category_ids or not isinstance(category_ids, list):
            raise BadRequest("category_ids must be a non-empty list")

        # 先删除该 app_id 下所有扩展
        app_extension_service.delete_app_extension(app_id)

        # 再批量插入
        extensions = app_extension_service.bulk_create_app_extensions(
            app_id=app_id,
            type_=type_,
            category_ids=category_ids,
            description=description
        )
        return {"result": "success", "items": extensions}, 201


class AppExtensionDeleteApi(Resource):
    @setup_required
    @login_required
    @marshal_with({"result": fields.String, "deleted": fields.Integer})
    def delete(self, app_id):
        """
        根据 app_id 删除应用的所有场景
        """
        app_id = str(app_id)
        with Session(db.engine) as session:
            deleted_count = app_extension_service.delete_app_extension(app_id, session=session)
            session.commit()  # 提交事务
        return {"result": "success", "deleted": deleted_count}, 200


class AppExtensionCategoryListApi(Resource):
    @setup_required
    @login_required
    @marshal_with({"items": fields.List(fields.Nested(category_fields))})
    def get(self):
        """
        获取所有场景（category）列表
        """
        categories = app_extension_service.list_categories()
        return {"items": categories}


class AppExtensionListByCategoryApi(Resource):
    @swagger_api(
        app_extension_list_response_fields,
        tags=["应用扩展"],
        summary="根据场景查询应用扩展列表",
        description="根据场景ID查询应用扩展列表，支持特殊值：myself（我的应用）、all（所有可访问应用）",
        path_params={"category_id": "场景ID，支持特殊值：myself（我的应用）、all（所有可访问应用）"},
        query_params={
            "page": ("页码", "integer", False, 1),
            "limit": ("每页数量", "integer", False, 20),
            "name": "按名称搜索"
        }
    )
    @setup_required
    @login_required
    @privilege_required(RolePrivilegeType.APP_CENTER)
    def get(self, category_id):
        """
        根据场景（category_id）查询该场景下的所有扩展，带上App和Site信息，支持分页和权限过滤
        新增：支持只查自己发布的应用
        """

        category_id = str(category_id)
        page = int(request.args.get("page", 1))
        limit = int(request.args.get("limit", 20))
        name = request.args.get("name")  # 获取 name 参数
        # 确保 name 参数有实际值时才传递，避免空字符串影响查询
        name = name.strip() if name else None
        offical_str = request.args.get("offical", "false")
        offical = offical_str.lower() in ("true", "1", "yes")
        logger.info(f"offical param: {offical_str} -> {offical}, department_id will be: {None if offical else 'current_user.department_id'}, account_id will be: {None if offical else 'current_user.id'}")
       
        # 获取当前用户的部门和账号ID
        email = getattr(current_user, "email", None)
        department_id = getattr(current_user, "department_id", None)
        account_id = getattr(current_user, "id", None)
        current_tenant_id = current_user.current_tenant_id
        # officalApp = hasPrivilege(RolePrivilegeType.OFFICIAL_APP)

        if category_id == "myself":
            paged_results, total = app_extension_service.list_my_apps_with_first_extension_and_site(
                page=page, limit=limit, account_id=account_id, name=name
            )
        elif category_id == "all":
            paged_results, total = app_extension_service.list_accessible_apps_with_first_extension_and_site(
                page=page, limit=limit, department_id=None if offical else department_id,
                account_id=None if offical else account_id, name=name
            )
        else:
            paged_results, total = app_extension_service.list_app_extensions_with_app_and_site_by_category(
                category_id,
                page=page,
                limit=limit,
                department_id=None if offical else department_id,
                account_id=None if offical else account_id,
                name=name
            )

        items = []
        for ext, app, site in paged_results:
            # 查询创建人
            creator_name = app.accountName if app.accountName else ""
            # 查询当前租户是否已安装该 app
            installed_app = db.session.query(InstalledApp).filter_by(
                app_id=app.id,
                tenant_id=current_tenant_id
            ).first()
            if not installed_app:
                # 没有则自动新增
                installed_app = InstalledApp(
                    app_id=app.id,
                    tenant_id=current_tenant_id,
                    app_owner_tenant_id=app.tenant_id,
                    is_pinned=False,
                )
                db.session.add(installed_app)
                db.session.commit()
            items.append({
                "extension": ext,
                "app": app,
                "site": site,
                "installed_app_id": installed_app.id if installed_app else None,  # 返回ID
                "creator_name": creator_name if creator_name else "",
            })
        return {
            "items": items,
            "total": total,
            "page": page,
            "limit": limit,
        }


class OfficialAppApi(Resource):
    # 获取官方应用列表
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """Get official app list"""
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=30, type=int)
        name = request.args.get("name", default="", type=str)
        # app_service = AppService()
        apps, total = app_extension_service.listOfficialApps(page, limit, name)
        # current_tenant_id = current_user.current_tenant_id
        data = []
        for app in apps:
            resp = marshal(app, app_partial_fields)
            resp["is_published"] = True
            resp["app_base_url"] = dify_config.APP_WEB_URL or request.url_root.rstrip("/")
            # installed_app = db.session.query(InstalledApp).filter_by(app_id=app.id, tenant_id=current_tenant_id).first()
            # resp["installed_app_id"] = installed_app.id if installed_app else None
            data.append(resp)

        response = {"data": data, "total": total, "page": page, "limit": limit}
        return response


class ConfHomePageApi(Resource):
    # 获取首页设置
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        data = app_extension_service.getHomePageConfig()
        return {"data": [marshal(item, configs_fields) for item in data]}

    # 编辑首页设置
    @setup_required
    @login_required
    @account_initialization_required
    def put(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", nullable=False, type=str)
        parser.add_argument("type", nullable=False, type=str)
        parser.add_argument("content", nullable=False, type=str)
        # parser.add_argument("sequence", nullable=True, type=int)
        args = parser.parse_args()
        app_extension_service.editHomePageConfig(args)
        return {"result": "success"}


class ConfAppCenterApi(Resource):
    # 获取应用中心设置
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        data = app_extension_service.getAppCenterConfig()
        return {"data": [marshal(item, configs_fields) for item in data]}

    # 编辑应用中心设置
    @setup_required
    @login_required
    @account_initialization_required
    def put(self):
        parser = reqparse.RequestParser()
        parser.add_argument("id", nullable=False, type=str)
        parser.add_argument("type", nullable=False, type=str)
        parser.add_argument("content", nullable=False, type=str)
        # parser.add_argument("sequence", nullable=True, type=int)
        args = parser.parse_args()
        app_extension_service.editAppCenterConfig(args)
        return {"result": "success"}


class AppAccessibleApi(Resource):
    # 校验是否具有app使用权限
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, app_id):
        return {"installed_app_id": app_extension_service.accessibel(app_id)}


# 路由注册
api.add_resource(AppExtensionPublishApi, "/apps/<uuid:app_id>/extension/publish")
api.add_resource(AppExtensionDeleteApi, "/apps/<uuid:app_id>/extension")
api.add_resource(AppExtensionCategoryListApi, "/apps/extension/categories")
api.add_resource(AppExtensionListByCategoryApi, "/apps/extension/categories/<string:category_id>")
api.add_resource(OfficialAppApi, "/officialapp")
api.add_resource(ConfHomePageApi, "/homepage")
api.add_resource(ConfAppCenterApi, "/appcenter")
api.add_resource(AppAccessibleApi, "/apps/<uuid:app_id>/accessible")
