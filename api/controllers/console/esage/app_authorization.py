import logging
from flask_login import current_user  # type: ignore
from flask_restful import Resource, inputs, marshal, reqparse  # type: ignore

from libs.helper import uuid_value

from controllers.console.wraps import setup_required, privilege_required
from controllers.console.error import InvalidParameterError, ResourceNotFoundError
from libs.login import login_required
from controllers.console import api

from services.esage.app_extension_service import AppExtensionService
from fields.app_extension_fields import (
    app_authorization_list_fields,
    app_authorization_pagination_fields,
)

from models import App
from models.account import RolePrivilegeType
from extensions.ext_database import db

logger = logging.getLogger(__name__)


class AppAuthorizationApi(Resource):
    @setup_required
    @login_required
    # @privilege_required(RolePrivilegeType.PROJECT_SPACE)
    def get(self):
        """Get app authorization information."""
        logger.info("Get app authorization information")
        parser = reqparse.RequestParser()
        parser.add_argument(
            "page",
            type=inputs.int_range(1, 9999),
            required=False,
            default=1,
            location="args",
        )
        parser.add_argument(
            "limit",
            type=inputs.int_range(1, 100),
            required=False,
            default=20,
            location="args",
        )
        parser.add_argument("app_id", type=uuid_value, required=False, location="args")
        parser.add_argument("name", type=str, required=False, location="args")

        logger.info("Get app authorization information args: %s", parser)
        args = parser.parse_args()
        app_extension_service = AppExtensionService()

        app_authorization_pagination = (
            app_extension_service.get_pagination_app_authorization(
                current_user.id,
                current_user.current_tenant_id,
                args,
            )
        )
        if not app_authorization_pagination:
            return {"data": [], "total": 0, "page": 1, "limit": 20, "has_more": False}

        return marshal(
            app_authorization_pagination, app_authorization_pagination_fields
        )

    @setup_required
    @login_required
    # @privilege_required(RolePrivilegeType.PROJECT_SPACE)
    def post(self):
        """create app authorization"""
        parser = reqparse.RequestParser()
        parser.add_argument("app_id", type=uuid_value, required=True, location="json")
        parser.add_argument("authorizations", type=list, required=True, location="json")

        args = parser.parse_args()
        for auth in args["authorizations"]:
            if not auth.get("auth_type"):
                raise InvalidParameterError("auth_type is required")
            if auth["auth_type"] not in ["DEPARTMENT", "ACCOUNT"]:
                raise InvalidParameterError("auth_type is invalid")
            if auth["auth_type"] == "DEPARTMENT" and not auth.get("department_id"):
                raise InvalidParameterError(
                    "department_id is required for type DEPARTMENT"
                )
            if auth["auth_type"] == "ACCOUNT" and not auth.get("account_id"):
                raise InvalidParameterError("account_id is required for type ACCOUNT")

        if not db.session.query(App).filter(App.id == args["app_id"]).first():
            raise ResourceNotFoundError("app[id=%s] not found", args["app_id"])

        app_extension_service = AppExtensionService()
        app_authorizations = app_extension_service.create_app_authorization(
            current_user.id,
            current_user.current_tenant_id,
            args,
        )

        return marshal({"data": app_authorizations}, app_authorization_list_fields)

    @setup_required
    @login_required
    # @privilege_required(RolePrivilegeType.PROJECT_SPACE)
    def delete(self):
        """delete app authorization"""
        parser = reqparse.RequestParser()
        parser.add_argument("app_id", type=uuid_value, required=True, location="json")
        parser.add_argument("auth_ids", type=list, required=True, location="json")

        args = parser.parse_args()
        if not db.session.query(App).filter(App.id == args["app_id"]).first():
            raise ResourceNotFoundError("app[id=%s] not found", args["app_id"])
        if not args["auth_ids"]:
            raise InvalidParameterError("auth_ids is required")

        app_extension_service = AppExtensionService()
        app_authorizations = app_extension_service.delete_app_authorization(
            current_user.id,
            current_user.current_tenant_id,
            args,
        )

        return marshal({"data": app_authorizations}, app_authorization_list_fields)


api.add_resource(AppAuthorizationApi, "/app-extensions/authorization")
