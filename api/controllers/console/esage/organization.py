from fields.organization import (
    department_tree_fields,
    department_detail_fields,
    account_detail_fields,
    role_detail_fields,
    privilege_detail_fields,
    account_privilege_list_fields, resource_detail_fields
)
import re
import logging
from libs.password import valid_password
from flask import request
from controllers.console import api
from flask_restful import Resource, reqparse, marshal
from werkzeug.exceptions import Conflict, BadRequest
from controllers.console.wraps import account_initialization_required, setup_required, privilege_required, \
    privilege_required_any
from libs.login import login_required
from models.account import RolePrivilegeType
from services.esage.organization_service import OrganizationService
from services.message_service import MessageService


def _validate_name(name):
    if not name or len(name) < 2 or len(name) > 30:
        raise ValueError("名称为必填,长度2-30个字符.")
    return name


# def _validate_password(passwd):
#     pattern = r'^(?=.*[a-zA-Z])(?=.*\d).{6,}$'
#     if not bool(re.match(pattern, passwd)):
#         raise ValueError("密码必须包含字母和数字，且长度不小于6位.")
#     return passwd


class RolePrivilegeApi(Resource):

    # 查询角色包含权限
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, role_id: str):
        rolePrivileges = OrganizationService.getRolePrivilege(role_id)
        response = {"data": marshal(rolePrivileges, privilege_detail_fields)}
        return response


class RolesApi(Resource):

    # 查询角色列表
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.ROLE_MANAGEMENT)
    def get(self):
        # logging.info(f"getUsers")
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=30, type=int)
        name = request.args.get("name", default="", type=str)
        roles, total = OrganizationService.listRoles(page, limit, name)
        response = {"data": marshal(roles, role_detail_fields), "total": total}
        return response

    # 创建角色
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.ROLE_MANAGEMENT)
    def post(self):
        # logging.info(f"postUsers")
        parser = reqparse.RequestParser()
        parser.add_argument("name", nullable=False, required=True, type=_validate_name,
                            help="name is required. Name must be between 2 to 30 characters.", )
        parser.add_argument("description", nullable=True, required=False, type=str)
        args = parser.parse_args()
        OrganizationService.saveRole(args)
        return {"result": "success"}


class RoleApi(Resource):

    # 编辑角色及权限
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.ROLE_MANAGEMENT)
    def put(self, role_id: str):
        role = OrganizationService.getRole(role_id, True)
        if role.name == '系统管理员':
            raise Conflict("该角色不允许编辑和删除.")

        parser = reqparse.RequestParser()
        parser.add_argument("name", nullable=True, required=False, type=_validate_name,
                            help="name is required. Name must be between 2 to 30 characters.")
        parser.add_argument("description", nullable=True, required=False, type=str)
        parser.add_argument("privilege", nullable=True, required=False, type=str)

        args = parser.parse_args()
        OrganizationService.editRole(role_id, args)
        return {"result": "success"}

    # 删除角色
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.ROLE_MANAGEMENT)
    def delete(self, role_id: str):
        role = OrganizationService.getRole(role_id, True)
        if role.name == '系统管理员':
            raise Conflict("该角色不允许编辑和删除.")

        OrganizationService.deleteRole(role_id)
        return {"result": "success"}


class AccountsApi(Resource):

    # 查询账号列表
    @setup_required
    @login_required
    @account_initialization_required
    # @privilege_required_any([RolePrivilegeType.MEMBER_MANAGEMENT, RolePrivilegeType.PROJECT_SPACE])
    def get(self):
        # logging.info(f"getUsers")
        page = request.args.get("page", default=1, type=int)
        limit = request.args.get("limit", default=30, type=int)
        name = request.args.get("name", default="", type=str)
        status = request.args.get("status", default="", type=str)
        department_id = request.args.get("department_id", default="", type=str)
        orderBy = request.args.get("orderBy", default="updated_at", type=str)
        desc = request.args.get("desc", default="true", type=str)

        data, total = OrganizationService.listAccounts(page, limit, name, status, department_id, orderBy, desc)
        response = {"data": [marshal(item, account_detail_fields) for item in data], "total": total}
        return response

    # 创建账号
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def post(self):
        # logging.info(f"postUsers")
        parser = reqparse.RequestParser()
        parser.add_argument("name", nullable=False, required=True, type=_validate_name)
        parser.add_argument("email", nullable=False, required=True, type=str)
        parser.add_argument("department_id", nullable=True, required=False, type=str)
        parser.add_argument("role_id", nullable=False, required=True, type=str)
        parser.add_argument("password", nullable=False, required=True, type=valid_password)
        args = parser.parse_args()
        OrganizationService.saveAccount(args)
        return {"result": "success"}


class AccountApi(Resource):
    # 查询单个账号
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, account_id: str):
        account = OrganizationService.getAccount(account_id)
        accountRoles = OrganizationService.getAccountRole(account.id)
        account.role = []
        for ar in accountRoles:
            # logging.info(f"{account.name} roleId: {ar.role_id}")
            role = OrganizationService.getRole(ar.role_id, False)
            if role:
                account.role.append({"id": role.id, "name": role.name})

        # logging.info(f"roles: {account.role}")
        if account.department_id is not None:
            department = OrganizationService.getDepartment(account.department_id, False)
            account.department = department.name if department else ""

        return {"data": marshal(account, account_detail_fields)}

    # 编辑/启用,禁用账号
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def put(self, account_id: str):
        account = OrganizationService.getAccount(account_id)
        if account.name == "admin":
            raise Conflict("This account is not allowed to modify")

        parser = reqparse.RequestParser()
        parser.add_argument("enabled", nullable=True, required=False, type=bool)
        args = parser.parse_args()
        # logging.info(f"args: {args}")
        if args["enabled"] is not None:
            OrganizationService.enableAccount(account_id, args["enabled"])
        else:
            parser.add_argument("name", nullable=False, required=True, type=_validate_name,
                                help="name is required. Name must be between 2 to 30 characters.")
            parser.add_argument("department_id", nullable=True, required=False, type=str)
            parser.add_argument("role_id", nullable=False, required=True, type=str)
            parser.add_argument("password", nullable=True, required=False, type=str)
            args = parser.parse_args()
            OrganizationService.editAccount(account_id, args)

        return {"result": "success"}

    # 删除账号
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def delete(self, account_id: str):
        deleteComplete = request.args.get("deleteComplete", default=False, type=bool)
        OrganizationService.deleteAccount(account_id,deleteComplete)
        return {"result": "success"}


class AccountPrivilegeApi(Resource):
    # 查询账号包含权限
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, account_id: str):
        account_privileges = OrganizationService.get_account_privilege(account_id)
        return marshal({"data": account_privileges}, account_privilege_list_fields)


class AccountResourceNumApi(Resource):
    # 查询账号包含资源数量
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, account_id: str):
        resource = OrganizationService.get_account_resourceNum(account_id)
        # logging.info(f"resource: {resource}")
        return marshal(resource, resource_detail_fields)


class DepartmentTreeApi(Resource):
    # 查询部门树
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        tree = OrganizationService.getDepartmentTree()
        # logging.info(f"response: {tree}")
        response = {"data": marshal(tree, department_tree_fields)}
        return response


class DepartmentsApi(Resource):

    # 查询部门列表,department_id:上级部门ID
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        # logging.info(f"getUsers")
        department_id = request.args.get("department_id", default=None)
        departments = OrganizationService.listDepartment(department_id)
        response = {"data": [marshal(item, department_detail_fields) for item in departments]}
        return response

    # 创建部门
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def post(self):
        # logging.info(f"postUsers")
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            nullable=False,
            required=True,
            type=_validate_name,
            help="name is required. Name must be between 2 to 30 characters.",
        )
        parser.add_argument(
            "parent",
            nullable=False,
            required=True,
            help="parent parameter is required.",
            type=str,
        )
        args = parser.parse_args()
        OrganizationService.saveDepartment(args)
        return {"result": "success"}


class DepartmentApi(Resource):

    # 编辑部门
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def put(self, department_id: str):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "name",
            nullable=False,
            required=True,
            type=_validate_name,
            help="name is required. Name must be between 2 to 30 characters.",
        )
        args = parser.parse_args()
        OrganizationService.editDepartment(department_id, args)
        return {"result": "success"}

    # 删除部门
    @setup_required
    @login_required
    @account_initialization_required
    @privilege_required(RolePrivilegeType.MEMBER_MANAGEMENT)
    def delete(self, department_id: str):
        OrganizationService.deleteDepartment(department_id)
        return {"result": "success"}


class ValidNameExistApi(Resource):

    # 校验名字是否存在
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        name = request.args.get("name", type=str)
        if not name:
            raise BadRequest("name is required.")

        type = request.args.get("type", default="account", type=str)
        id = request.args.get("id", type=str)
        exist = OrganizationService.validNameExist(name, type, id)
        return {"exist": exist}


class MessageWorkingLinkApi(Resource):

    # 设置工作链路
    @setup_required
    def put(self, message_id: str):
        parser = reqparse.RequestParser()
        parser.add_argument("working_link", required=True, type=str)

        args = parser.parse_args()
        MessageService.confWorkingLink(message_id, args["working_link"])
        return {"result": "success"}


# Register API resources
api.add_resource(DepartmentTreeApi, "/departmentTree")
api.add_resource(DepartmentsApi, "/departments")
api.add_resource(DepartmentApi, "/departments/<uuid:department_id>")
api.add_resource(AccountsApi, "/accounts")
api.add_resource(AccountApi, "/accounts/<uuid:account_id>")
api.add_resource(AccountPrivilegeApi, "/accounts/<uuid:account_id>/privilege")
api.add_resource(AccountResourceNumApi, "/accounts/<uuid:account_id>/resourceNum")
api.add_resource(RolesApi, "/roles")
api.add_resource(RoleApi, "/roles/<uuid:role_id>")
api.add_resource(RolePrivilegeApi, "/roles/<uuid:role_id>/privilege")
api.add_resource(ValidNameExistApi, "/validNameExist")
api.add_resource(MessageWorkingLinkApi, "/messages/<uuid:message_id>/workinglink")
