import json
import datetime
from typing import cast

from flask import request
from flask_login import current_user  # type: ignore
from flask_restful import Resource  # type: ignore
from sqlalchemy.orm import Session

from controllers.console import api
from controllers.console.app.wraps import get_app_model
from controllers.console.wraps import account_initialization_required, setup_required
from core.agent.entities import AgentToolEntity
from core.tools.tool_manager import ToolManager
from core.tools.utils.configuration import ToolParameterConfigurationManager
from events.app_event import app_model_config_was_updated
from extensions.ext_database import db
from libs.login import login_required
from models.model import AppMode, AppModelConfig
from services.app_model_config_service import AppModelConfigService
from services.esage.app_extension_service import AppExtensionService


class ModelConfigResource(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model(mode=[AppMode.AGENT_CHAT, AppMode.CHAT, AppMode.COMPLETION])
    def post(self, app_model):
        """发布模型配置（有草稿则更新，无草稿则新增草稿，然后用草稿内容发布）"""
        app_extension_service = AppExtensionService()
        app_id = str(app_model.id)

        category_ids = request.json.get("category_ids")
        type_ = request.json.get("type", "normal")
        description = request.json.get("description")

        # 校验并准备本次请求的配置
        model_configuration = AppModelConfigService.validate_configuration(
            tenant_id=current_user.current_tenant_id,
            config=cast(dict, request.json),
            app_mode=AppMode.value_of(app_model.mode),
        )

        with Session(db.engine) as session:
            # 1. 处理草稿：有则更新，无则新增
            draft_config = session.query(AppModelConfig).filter_by(app_id=app_model.id, is_draft=True).first()
            if draft_config:
                draft_config = draft_config.from_model_config_dict(model_configuration)
                draft_config.updated_by = current_user.id
            else:
                draft_config = AppModelConfig(
                    app_id=app_model.id,
                    created_by=current_user.id,
                    updated_by=current_user.id,
                    is_draft=True,
                )
                draft_config = draft_config.from_model_config_dict(model_configuration)
                session.add(draft_config)
                session.flush()

            # 2. 用草稿内容发布
           # model_configuration = draft_config.to_model_config_dict()

            # 3. 处理 category_ids 逻辑
            if not category_ids or not isinstance(category_ids, list) or len(category_ids) == 0:
                app_extension_service.delete_app_extension(app_id, session=session)
            else:
                app_extension_service.delete_app_extension(app_id, session=session)
                app_extension_service.bulk_create_app_extensions(
                    app_id=app_id,
                    type_=type_,
                    category_ids=category_ids,
                    description=description,
                    session=session,
                )

            # 4. 新增正式配置，不删除历史
            new_app_model_config = AppModelConfig(
                app_id=app_model.id,
                created_by=current_user.id,
                updated_by=current_user.id,
                is_draft=False,
            )
            new_app_model_config = new_app_model_config.from_model_config_dict(model_configuration)

            # 5. agent_mode 相关逻辑（保持原有）
            if app_model.mode == AppMode.AGENT_CHAT.value or app_model.is_agent:
                original_app_model_config = (
                    db.session.query(AppModelConfig).filter(AppModelConfig.id == app_model.app_model_config_id).first()
                )
                if original_app_model_config is None:
                    raise ValueError("Original app model config not found")
                agent_mode = original_app_model_config.agent_mode_dict
                parameter_map = {}
                masked_parameter_map = {}
                tool_map = {}
                for tool in agent_mode.get("tools") or []:
                    if not isinstance(tool, dict) or len(tool.keys()) <= 3:
                        continue
                    agent_tool_entity = AgentToolEntity(**tool)
                    try:
                        tool_runtime = ToolManager.get_agent_tool_runtime(
                            tenant_id=current_user.current_tenant_id,
                            app_id=app_model.id,
                            agent_tool=agent_tool_entity,
                        )
                        manager = ToolParameterConfigurationManager(
                            tenant_id=current_user.current_tenant_id,
                            tool_runtime=tool_runtime,
                            provider_name=agent_tool_entity.provider_id,
                            provider_type=agent_tool_entity.provider_type,
                            identity_id=f"AGENT.{app_model.id}",
                        )
                    except Exception:
                        continue
                    if agent_tool_entity.tool_parameters:
                        parameters = manager.decrypt_tool_parameters(agent_tool_entity.tool_parameters or {})
                        masked_parameter = manager.mask_tool_parameters(parameters or {})
                    else:
                        parameters = {}
                        masked_parameter = {}
                    key = f"{agent_tool_entity.provider_id}.{agent_tool_entity.provider_type}.{agent_tool_entity.tool_name}"
                    masked_parameter_map[key] = masked_parameter
                    parameter_map[key] = parameters
                    tool_map[key] = tool_runtime

                agent_mode = new_app_model_config.agent_mode_dict
                for tool in agent_mode.get("tools") or []:
                    agent_tool_entity = AgentToolEntity(**tool)
                    key = f"{agent_tool_entity.provider_id}.{agent_tool_entity.provider_type}.{agent_tool_entity.tool_name}"
                    if key in tool_map:
                        tool_runtime = tool_map[key]
                    else:
                        try:
                            tool_runtime = ToolManager.get_agent_tool_runtime(
                                tenant_id=current_user.current_tenant_id,
                                app_id=app_model.id,
                                agent_tool=agent_tool_entity,
                            )
                        except Exception:
                            continue
                    manager = ToolParameterConfigurationManager(
                        tenant_id=current_user.current_tenant_id,
                        tool_runtime=tool_runtime,
                        provider_name=agent_tool_entity.provider_id,
                        provider_type=agent_tool_entity.provider_type,
                        identity_id=f"AGENT.{app_model.id}",
                    )
                    manager.delete_tool_parameters_cache()
                    if agent_tool_entity.tool_parameters:
                        if key not in masked_parameter_map:
                            continue
                        for masked_key, masked_value in masked_parameter_map[key].items():
                            if (
                                masked_key in agent_tool_entity.tool_parameters
                                and agent_tool_entity.tool_parameters[masked_key] == masked_value
                            ):
                                agent_tool_entity.tool_parameters[masked_key] = parameter_map[key].get(masked_key)
                    if agent_tool_entity.tool_parameters:
                        tool["tool_parameters"] = manager.encrypt_tool_parameters(agent_tool_entity.tool_parameters or {})
                new_app_model_config.agent_mode = json.dumps(agent_mode)

            session.add(new_app_model_config)
            session.flush()

            # 6. 更新 app 的 model_config_id 指向最新
            app_model.app_model_config_id = new_app_model_config.id
            session.commit()

            app_model_config_was_updated.send(app_model, app_model_config=new_app_model_config)

        return {"result": "success"}


class ModelConfigDraftResource(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    @get_app_model(mode=[AppMode.AGENT_CHAT, AppMode.CHAT, AppMode.COMPLETION])
    def post(self, app_model):
        """保存或更新模型配置草稿（有则更新，无则新增）"""
        model_configuration = AppModelConfigService.validate_configuration(
            tenant_id=current_user.current_tenant_id,
            config=cast(dict, request.json),
            app_mode=AppMode.value_of(app_model.mode),
        )
        with Session(db.engine) as session:
            draft_config = session.query(AppModelConfig).filter_by(app_id=app_model.id, is_draft=True).first()
            if draft_config:
                # 更新草稿内容
                draft_config = draft_config.from_model_config_dict(model_configuration)
                draft_config.updated_by = current_user.id
                draft_config.updated_at = datetime.datetime.now()  # 新增更新时间
            else:
                # 新增草稿
                draft_config = AppModelConfig(
                    app_id=app_model.id,
                    created_by=current_user.id,
                    updated_by=current_user.id,
                    is_draft=True,
                )
                draft_config = draft_config.from_model_config_dict(model_configuration)
                session.add(draft_config)
            session.commit()
        return {"result": "success"}


api.add_resource(ModelConfigResource, "/apps/<uuid:app_id>/model-config")
api.add_resource(ModelConfigDraftResource, "/apps/<uuid:app_id>/model-config/draft")
