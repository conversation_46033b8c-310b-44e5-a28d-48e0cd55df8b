from events.department_event import department_was_deleted
from extensions.ext_database import db
from models.app_extension import AppAuthorization


@department_was_deleted.connect
def handle(sender, **kwargs):
    """Clean app authorizations when a department is deleted."""
    department = sender
    if department is not None:
        db.session.query(AppAuthorization).filter(
            AppAuthorization.department_id == department.id
        ).delete(synchronize_session=False)

        db.session.commit()
